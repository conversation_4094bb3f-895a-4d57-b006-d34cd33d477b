import axios from 'axios';
import { API_BASE_URL } from '../config/constants';

const API_URL = `${API_BASE_URL}/subscriptions`;

// Mock data for development
const USE_MOCK_DATA = true;

class SubscriptionService {
  // Get all subscription plans
  async getPlans() {
    if (USE_MOCK_DATA) {
      return this.getMockPlans();
    }

    try {
      const response = await axios.get(`${API_URL}/plans`);
      return response.data;
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      throw error;
    }
  }

  // Get current user subscription
  async getCurrentSubscription() {
    if (USE_MOCK_DATA) {
      return this.getMockCurrentSubscription();
    }

    try {
      const response = await axios.get(`${API_URL}/current`);
      return response.data;
    } catch (error) {
      console.error('Error fetching current subscription:', error);
      throw error;
    }
  }

  // Subscribe to a plan
  async subscribe(planId, paymentMethod, paymentToken, autoRenew = true) {
    if (USE_MOCK_DATA) {
      return this.getMockSubscribe(planId, paymentMethod);
    }

    try {
      const response = await axios.post(`${API_URL}/subscribe`, {
        plan_id: planId,
        payment_method: paymentMethod,
        payment_token: paymentToken,
        auto_renew: autoRenew
      });
      return response.data;
    } catch (error) {
      console.error('Error subscribing to plan:', error);
      throw error;
    }
  }

  // Cancel subscription
  async cancelSubscription(reason = null) {
    if (USE_MOCK_DATA) {
      return this.getMockCancelSubscription();
    }

    try {
      const response = await axios.post(`${API_URL}/cancel`, {
        reason: reason
      });
      return response.data;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  // Renew subscription
  async renewSubscription(paymentMethod, paymentToken) {
    if (USE_MOCK_DATA) {
      return this.getMockRenewSubscription();
    }

    try {
      const response = await axios.post(`${API_URL}/renew`, {
        payment_method: paymentMethod,
        payment_token: paymentToken
      });
      return response.data;
    } catch (error) {
      console.error('Error renewing subscription:', error);
      throw error;
    }
  }

  // Get subscription history
  async getSubscriptionHistory() {
    if (USE_MOCK_DATA) {
      return this.getMockSubscriptionHistory();
    }

    try {
      const response = await axios.get(`${API_URL}/history`);
      return response.data;
    } catch (error) {
      console.error('Error fetching subscription history:', error);
      throw error;
    }
  }

  // Get payment history
  async getPaymentHistory() {
    if (USE_MOCK_DATA) {
      return this.getMockPaymentHistory();
    }

    try {
      const response = await axios.get(`${API_URL}/payments`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  }

  // Check usage limits
  async checkUsage() {
    if (USE_MOCK_DATA) {
      return this.getMockUsage();
    }

    try {
      const response = await axios.get(`${API_URL}/usage`);
      return response.data;
    } catch (error) {
      console.error('Error fetching usage data:', error);
      throw error;
    }
  }

  // Mock data methods
  getMockPlans() {
    return {
      status: 'success',
      data: [
        {
          id: 1,
          name: 'Basic',
          slug: 'basic',
          description: 'Perfect for small farmers and individual traders',
          price: 29.99,
          billing_cycle: 'monthly',
          billing_cycle_label: 'Monthly',
          duration_days: 30,
          features: ['ai_analytics', 'priority_support'],
          limits: {
            products: 50,
            contracts: 20,
            advertisements: 5
          },
          is_popular: false,
          is_active: true,
          trial_days: 7,
          setup_fee: 0
        },
        {
          id: 2,
          name: 'Professional',
          slug: 'professional',
          description: 'Ideal for growing businesses and cooperatives',
          price: 79.99,
          billing_cycle: 'monthly',
          billing_cycle_label: 'Monthly',
          duration_days: 30,
          features: ['unlimited_products', 'ai_analytics', 'priority_support', 'advanced_reports'],
          limits: {
            products: -1,
            contracts: 100,
            advertisements: 20
          },
          is_popular: true,
          is_active: true,
          trial_days: 14,
          setup_fee: 0
        },
        {
          id: 3,
          name: 'Enterprise',
          slug: 'enterprise',
          description: 'Complete solution for large organizations',
          price: 199.99,
          billing_cycle: 'monthly',
          billing_cycle_label: 'Monthly',
          duration_days: 30,
          features: ['unlimited_products', 'unlimited_contracts', 'ai_analytics', 'priority_support', 'advanced_reports', 'custom_branding', 'api_access', 'dedicated_manager'],
          limits: {
            products: -1,
            contracts: -1,
            advertisements: -1
          },
          is_popular: false,
          is_active: true,
          trial_days: 30,
          setup_fee: 99.99
        },
        // Yearly plans
        {
          id: 4,
          name: 'Basic',
          slug: 'basic-yearly',
          description: 'Perfect for small farmers and individual traders',
          price: 299.99,
          billing_cycle: 'yearly',
          billing_cycle_label: 'Yearly',
          duration_days: 365,
          features: ['ai_analytics', 'priority_support'],
          limits: {
            products: 50,
            contracts: 20,
            advertisements: 5
          },
          is_popular: false,
          is_active: true,
          trial_days: 7,
          setup_fee: 0
        },
        {
          id: 5,
          name: 'Professional',
          slug: 'professional-yearly',
          description: 'Ideal for growing businesses and cooperatives',
          price: 799.99,
          billing_cycle: 'yearly',
          billing_cycle_label: 'Yearly',
          duration_days: 365,
          features: ['unlimited_products', 'ai_analytics', 'priority_support', 'advanced_reports'],
          limits: {
            products: -1,
            contracts: 100,
            advertisements: 20
          },
          is_popular: true,
          is_active: true,
          trial_days: 14,
          setup_fee: 0
        },
        {
          id: 6,
          name: 'Enterprise',
          slug: 'enterprise-yearly',
          description: 'Complete solution for large organizations',
          price: 1999.99,
          billing_cycle: 'yearly',
          billing_cycle_label: 'Yearly',
          duration_days: 365,
          features: ['unlimited_products', 'unlimited_contracts', 'ai_analytics', 'priority_support', 'advanced_reports', 'custom_branding', 'api_access', 'dedicated_manager'],
          limits: {
            products: -1,
            contracts: -1,
            advertisements: -1
          },
          is_popular: false,
          is_active: true,
          trial_days: 30,
          setup_fee: 99.99
        }
      ]
    };
  }

  getMockCurrentSubscription() {
    return {
      status: 'success',
      data: {
        subscription: {
          id: 1,
          status: 'active',
          starts_at: '2024-01-01T00:00:00Z',
          ends_at: '2024-02-01T00:00:00Z',
          subscription_plan_id: 2,
          subscriptionPlan: {
            id: 2,
            name: 'Professional',
            price: 79.99,
            billing_cycle: 'monthly'
          }
        },
        usage: {
          products: 25,
          contracts: 8,
          advertisements: 3
        },
        days_remaining: 15,
        is_active: true,
        is_trial: false,
        can_renew: false
      }
    };
  }

  getMockSubscribe(planId, paymentMethod) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Subscription created successfully',
          data: {
            subscription: {
              id: Math.floor(Math.random() * 1000),
              status: 'active',
              plan_id: planId,
              payment_method: paymentMethod
            }
          }
        });
      }, 2000);
    });
  }

  getMockCancelSubscription() {
    return {
      status: 'success',
      message: 'Subscription cancelled successfully'
    };
  }

  getMockRenewSubscription() {
    return {
      status: 'success',
      message: 'Subscription renewed successfully'
    };
  }

  getMockSubscriptionHistory() {
    return {
      status: 'success',
      data: {
        data: [
          {
            id: 1,
            status: 'active',
            starts_at: '2024-01-01',
            ends_at: '2024-02-01',
            subscriptionPlan: {
              name: 'Professional',
              price: 79.99
            }
          }
        ]
      }
    };
  }

  getMockPaymentHistory() {
    return {
      status: 'success',
      data: {
        data: [
          {
            id: 1,
            amount: 79.99,
            status: 'completed',
            payment_method: 'stripe',
            created_at: '2024-01-01',
            userSubscription: {
              subscriptionPlan: {
                name: 'Professional'
              }
            }
          }
        ]
      }
    };
  }

  getMockUsage() {
    return {
      status: 'success',
      data: {
        usage: {
          products: {
            limit: -1,
            used: 25,
            remaining: 'unlimited',
            percentage: 0
          },
          contracts: {
            limit: 100,
            used: 8,
            remaining: 92,
            percentage: 8
          },
          advertisements: {
            limit: 20,
            used: 3,
            remaining: 17,
            percentage: 15
          }
        }
      }
    };
  }
}

export default new SubscriptionService();
