import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

class CustomsService {
  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/customs`,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })
  }

  // Get list of countries
  async getCountries() {
    try {
      const response = await this.api.get('/countries')
      return response.data
    } catch (error) {
      console.error('Error fetching countries:', error)
      throw error
    }
  }

  // Get customs requirements for specific country and product
  async getCustomsRequirements(countryCode, productType) {
    try {
      const response = await this.api.get('/requirements', {
        params: { country: countryCode, product_type: productType }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching customs requirements:', error)
      throw error
    }
  }

  // Calculate tariff and taxes
  async calculateTariff(calculationData) {
    try {
      const response = await this.api.post('/calculate-tariff', calculationData)
      return response.data
    } catch (error) {
      console.error('Error calculating tariff:', error)
      throw error
    }
  }

  // Get HS codes for products
  async getHSCodes(productType = '', searchTerm = '') {
    try {
      const response = await this.api.get('/hs-codes', {
        params: { product_type: productType, search: searchTerm }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching HS codes:', error)
      throw error
    }
  }

  // Get customs documents library
  async getCustomsDocuments(filters = {}) {
    try {
      const response = await this.api.get('/documents', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching customs documents:', error)
      throw error
    }
  }

  // Download customs document
  async downloadDocument(documentId) {
    try {
      const response = await this.api.get(`/documents/${documentId}/download`, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error downloading document:', error)
      throw error
    }
  }

  // Get trade agreements between countries
  async getTradeAgreements(fromCountry, toCountry) {
    try {
      const response = await this.api.get('/trade-agreements', {
        params: { from: fromCountry, to: toCountry }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching trade agreements:', error)
      throw error
    }
  }

  // Get import/export restrictions
  async getRestrictions(countryCode, productType) {
    try {
      const response = await this.api.get('/restrictions', {
        params: { country: countryCode, product_type: productType }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching restrictions:', error)
      throw error
    }
  }

  // Get customs brokers in specific country
  async getCustomsBrokers(countryCode) {
    try {
      const response = await this.api.get('/brokers', {
        params: { country: countryCode }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching customs brokers:', error)
      throw error
    }
  }

  // Get shipping ports and customs facilities
  async getCustomsFacilities(countryCode, facilityType = 'all') {
    try {
      const response = await this.api.get('/facilities', {
        params: { country: countryCode, type: facilityType }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching customs facilities:', error)
      throw error
    }
  }

  // Get customs clearance timeline
  async getClearanceTimeline(countryCode, productType) {
    try {
      const response = await this.api.get('/clearance-timeline', {
        params: { country: countryCode, product_type: productType }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching clearance timeline:', error)
      throw error
    }
  }

  // Submit customs declaration
  async submitDeclaration(declarationData) {
    try {
      const response = await this.api.post('/declarations', declarationData)
      return response.data
    } catch (error) {
      console.error('Error submitting declaration:', error)
      throw error
    }
  }

  // Get customs declarations
  async getDeclarations(filters = {}) {
    try {
      const response = await this.api.get('/declarations', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching declarations:', error)
      throw error
    }
  }

  // Get declaration status
  async getDeclarationStatus(declarationId) {
    try {
      const response = await this.api.get(`/declarations/${declarationId}/status`)
      return response.data
    } catch (error) {
      console.error('Error fetching declaration status:', error)
      throw error
    }
  }

  // Update declaration
  async updateDeclaration(declarationId, updateData) {
    try {
      const response = await this.api.put(`/declarations/${declarationId}`, updateData)
      return response.data
    } catch (error) {
      console.error('Error updating declaration:', error)
      throw error
    }
  }

  // Get customs forms templates
  async getCustomsForms(countryCode, formType = '') {
    try {
      const response = await this.api.get('/forms', {
        params: { country: countryCode, type: formType }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching customs forms:', error)
      throw error
    }
  }

  // Download customs form
  async downloadCustomsForm(formId) {
    try {
      const response = await this.api.get(`/forms/${formId}/download`, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error downloading customs form:', error)
      throw error
    }
  }

  // Get duty rates and taxes
  async getDutyRates(countryCode, hsCode) {
    try {
      const response = await this.api.get('/duty-rates', {
        params: { country: countryCode, hs_code: hsCode }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching duty rates:', error)
      throw error
    }
  }

  // Get preferential tariff rates
  async getPreferentialRates(fromCountry, toCountry, hsCode) {
    try {
      const response = await this.api.get('/preferential-rates', {
        params: { from: fromCountry, to: toCountry, hs_code: hsCode }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching preferential rates:', error)
      throw error
    }
  }

  // Get customs regulations updates
  async getRegulationUpdates(countryCode = '', productType = '') {
    try {
      const response = await this.api.get('/regulation-updates', {
        params: { country: countryCode, product_type: productType }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching regulation updates:', error)
      throw error
    }
  }

  // Subscribe to regulation updates
  async subscribeToUpdates(subscriptionData) {
    try {
      const response = await this.api.post('/subscribe-updates', subscriptionData)
      return response.data
    } catch (error) {
      console.error('Error subscribing to updates:', error)
      throw error
    }
  }

  // Get customs contact information
  async getCustomsContacts(countryCode) {
    try {
      const response = await this.api.get('/contacts', {
        params: { country: countryCode }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching customs contacts:', error)
      throw error
    }
  }

  // Get customs FAQ
  async getCustomsFAQ(countryCode = '', category = '') {
    try {
      const response = await this.api.get('/faq', {
        params: { country: countryCode, category }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching customs FAQ:', error)
      throw error
    }
  }

  // Submit customs inquiry
  async submitInquiry(inquiryData) {
    try {
      const response = await this.api.post('/inquiries', inquiryData)
      return response.data
    } catch (error) {
      console.error('Error submitting inquiry:', error)
      throw error
    }
  }

  // Get customs inquiries
  async getInquiries() {
    try {
      const response = await this.api.get('/inquiries')
      return response.data
    } catch (error) {
      console.error('Error fetching inquiries:', error)
      throw error
    }
  }

  // Get customs statistics
  async getCustomsStatistics(countryCode, period = '12m') {
    try {
      const response = await this.api.get('/statistics', {
        params: { country: countryCode, period }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching customs statistics:', error)
      throw error
    }
  }

  // Get product classification assistance
  async getProductClassification(productDescription) {
    try {
      const response = await this.api.post('/classify-product', {
        description: productDescription
      })
      return response.data
    } catch (error) {
      console.error('Error getting product classification:', error)
      throw error
    }
  }

  // Get customs valuation methods
  async getValuationMethods(countryCode) {
    try {
      const response = await this.api.get('/valuation-methods', {
        params: { country: countryCode }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching valuation methods:', error)
      throw error
    }
  }

  // Calculate customs value
  async calculateCustomsValue(valuationData) {
    try {
      const response = await this.api.post('/calculate-value', valuationData)
      return response.data
    } catch (error) {
      console.error('Error calculating customs value:', error)
      throw error
    }
  }

  // Get origin rules
  async getOriginRules(fromCountry, toCountry, productType) {
    try {
      const response = await this.api.get('/origin-rules', {
        params: { from: fromCountry, to: toCountry, product_type: productType }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching origin rules:', error)
      throw error
    }
  }

  // Get certificate of origin requirements
  async getCertificateRequirements(fromCountry, toCountry) {
    try {
      const response = await this.api.get('/certificate-requirements', {
        params: { from: fromCountry, to: toCountry }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching certificate requirements:', error)
      throw error
    }
  }

  // Submit certificate of origin application
  async submitCertificateApplication(applicationData) {
    try {
      const response = await this.api.post('/certificate-applications', applicationData)
      return response.data
    } catch (error) {
      console.error('Error submitting certificate application:', error)
      throw error
    }
  }

  // Get customs alerts and notifications
  async getCustomsAlerts() {
    try {
      const response = await this.api.get('/alerts')
      return response.data
    } catch (error) {
      console.error('Error fetching customs alerts:', error)
      throw error
    }
  }

  // Mark alert as read
  async markAlertAsRead(alertId) {
    try {
      const response = await this.api.put(`/alerts/${alertId}/read`)
      return response.data
    } catch (error) {
      console.error('Error marking alert as read:', error)
      throw error
    }
  }
}

export const customsService = new CustomsService()
export { CustomsService }
