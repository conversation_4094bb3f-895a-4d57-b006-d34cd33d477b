import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'
import { 
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
  EyeIcon,
  PaperClipIcon
} from '@heroicons/react/24/outline'
import { DisputeService } from '../../services/disputeService'

const DisputeSystem = () => {
  const { t } = useTranslation()
  const [disputes, setDisputes] = useState([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedDispute, setSelectedDispute] = useState(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    priority: 'all'
  })

  useEffect(() => {
    fetchDisputes()
  }, [filters])

  const fetchDisputes = async () => {
    try {
      setLoading(true)
      const response = await DisputeService.getDisputes(filters)
      setDisputes(response.data)
    } catch (error) {
      console.error('Error fetching disputes:', error)
      toast.error(t('disputes.errorFetchingDisputes'))
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
      case 'in_progress':
        return <ClockIcon className="h-5 w-5 text-blue-500" />
      case 'resolved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'closed':
        return <XCircleIcon className="h-5 w-5 text-gray-500" />
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status) => {
    const statusClasses = {
      open: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-blue-100 text-blue-800',
      resolved: 'bg-green-100 text-green-800',
      closed: 'bg-gray-100 text-gray-800'
    }
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusClasses[status] || statusClasses.open}`}>
        {t(`disputes.status.${status}`)}
      </span>
    )
  }

  const getPriorityBadge = (priority) => {
    const priorityClasses = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    }
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${priorityClasses[priority] || priorityClasses.medium}`}>
        {t(`disputes.priority.${priority}`)}
      </span>
    )
  }

  const getDisputeTypeIcon = (type) => {
    switch (type) {
      case 'payment':
        return '💳'
      case 'quality':
        return '🔍'
      case 'delivery':
        return '🚚'
      case 'contract':
        return '📄'
      case 'communication':
        return '💬'
      default:
        return '❓'
    }
  }

  const handleCreateDispute = async (disputeData) => {
    try {
      await DisputeService.createDispute(disputeData)
      toast.success(t('disputes.disputeCreated'))
      setShowCreateModal(false)
      fetchDisputes()
    } catch (error) {
      console.error('Error creating dispute:', error)
      toast.error(t('disputes.errorCreatingDispute'))
    }
  }

  const handleUpdateDispute = async (disputeId, updateData) => {
    try {
      await DisputeService.updateDispute(disputeId, updateData)
      toast.success(t('disputes.disputeUpdated'))
      fetchDisputes()
    } catch (error) {
      console.error('Error updating dispute:', error)
      toast.error(t('disputes.errorUpdatingDispute'))
    }
  }

  const CreateDisputeModal = () => {
    const [formData, setFormData] = useState({
      type: '',
      priority: 'medium',
      title: '',
      description: '',
      contract_id: '',
      order_id: '',
      attachments: []
    })

    const handleSubmit = (e) => {
      e.preventDefault()
      handleCreateDispute(formData)
    }

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">{t('disputes.createDispute')}</h2>
            <button
              onClick={() => setShowCreateModal(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircleIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('disputes.type')}
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="">{t('disputes.selectType')}</option>
                  <option value="payment">{t('disputes.type.payment')}</option>
                  <option value="quality">{t('disputes.type.quality')}</option>
                  <option value="delivery">{t('disputes.type.delivery')}</option>
                  <option value="contract">{t('disputes.type.contract')}</option>
                  <option value="communication">{t('disputes.type.communication')}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('disputes.priority')}
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="low">{t('disputes.priority.low')}</option>
                  <option value="medium">{t('disputes.priority.medium')}</option>
                  <option value="high">{t('disputes.priority.high')}</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('disputes.title')}
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                placeholder={t('disputes.titlePlaceholder')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('disputes.description')}
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                required
                rows={4}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                placeholder={t('disputes.descriptionPlaceholder')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('disputes.contractId')} ({t('common.optional')})
                </label>
                <input
                  type="text"
                  value={formData.contract_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, contract_id: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="CON-12345"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('disputes.orderId')} ({t('common.optional')})
                </label>
                <input
                  type="text"
                  value={formData.order_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, order_id: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="ORD-12345"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('disputes.attachments')} ({t('common.optional')})
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <PaperClipIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">{t('disputes.attachmentsPlaceholder')}</p>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  className="hidden"
                  id="attachments"
                />
                <label
                  htmlFor="attachments"
                  className="mt-2 inline-block bg-gray-100 text-gray-700 px-4 py-2 rounded-lg cursor-pointer hover:bg-gray-200"
                >
                  {t('disputes.selectFiles')}
                </label>
              </div>
            </div>

            <div className="flex space-x-4 pt-4">
              <button
                type="button"
                onClick={() => setShowCreateModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                {t('disputes.createDispute')}
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {t('disputes.disputeSystem')}
                </h1>
                <p className="text-gray-600">
                  {t('disputes.manageAndResolveDisputes')}
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
            >
              <PlusIcon className="h-5 w-5" />
              <span>{t('disputes.createDispute')}</span>
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('disputes.openDisputes')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {disputes.filter(d => d.status === 'open').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('disputes.inProgress')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {disputes.filter(d => d.status === 'in_progress').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('disputes.resolved')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {disputes.filter(d => d.status === 'resolved').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChatBubbleLeftRightIcon className="h-8 w-8 text-purple-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('disputes.totalDisputes')}</p>
                <p className="text-2xl font-semibold text-gray-900">{disputes.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('disputes.status')}
              </label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="all">{t('disputes.allStatuses')}</option>
                <option value="open">{t('disputes.status.open')}</option>
                <option value="in_progress">{t('disputes.status.in_progress')}</option>
                <option value="resolved">{t('disputes.status.resolved')}</option>
                <option value="closed">{t('disputes.status.closed')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('disputes.type')}
              </label>
              <select
                value={filters.type}
                onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="all">{t('disputes.allTypes')}</option>
                <option value="payment">{t('disputes.type.payment')}</option>
                <option value="quality">{t('disputes.type.quality')}</option>
                <option value="delivery">{t('disputes.type.delivery')}</option>
                <option value="contract">{t('disputes.type.contract')}</option>
                <option value="communication">{t('disputes.type.communication')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('disputes.priority')}
              </label>
              <select
                value={filters.priority}
                onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="all">{t('disputes.allPriorities')}</option>
                <option value="high">{t('disputes.priority.high')}</option>
                <option value="medium">{t('disputes.priority.medium')}</option>
                <option value="low">{t('disputes.priority.low')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Disputes List */}
        <div className="space-y-4">
          {disputes.map((dispute) => (
            <div key={dispute.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">{getDisputeTypeIcon(dispute.type)}</div>
                  <div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(dispute.status)}
                      <h3 className="text-lg font-semibold text-gray-900">{dispute.title}</h3>
                    </div>
                    <p className="text-sm text-gray-500">
                      {t('disputes.disputeId')}: {dispute.dispute_id} • {t('disputes.createdOn')}: {new Date(dispute.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(dispute.status)}
                  {getPriorityBadge(dispute.priority)}
                </div>
              </div>

              <div className="mt-4">
                <p className="text-gray-700 line-clamp-2">{dispute.description}</p>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  {dispute.contract_id && (
                    <span>{t('disputes.contract')}: {dispute.contract_id}</span>
                  )}
                  {dispute.order_id && (
                    <span>{t('disputes.order')}: {dispute.order_id}</span>
                  )}
                  <span>{t('disputes.lastUpdate')}: {new Date(dispute.updated_at).toLocaleDateString()}</span>
                </div>
                <button
                  onClick={() => {
                    setSelectedDispute(dispute)
                    setShowDetailsModal(true)
                  }}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
                >
                  <EyeIcon className="h-4 w-4" />
                  <span>{t('disputes.viewDetails')}</span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {disputes.length === 0 && (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <ExclamationTriangleIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('disputes.noDisputes')}
            </h3>
            <p className="text-gray-500 mb-4">
              {t('disputes.noDisputesDescription')}
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              {t('disputes.createFirstDispute')}
            </button>
          </div>
        )}

        {/* Modals */}
        {showCreateModal && <CreateDisputeModal />}
      </div>
    </div>
  )
}

export default DisputeSystem
