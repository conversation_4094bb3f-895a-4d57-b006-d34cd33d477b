<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\ProductController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\ContractController;
use App\Http\Controllers\API\ShipmentController;
use App\Http\Controllers\API\MarketAnalysisController;
use App\Http\Controllers\API\AdvertisementController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\AIController;
use App\Http\Controllers\API\AdminController;
use App\Http\Controllers\API\ModeratorController;
use App\Http\Controllers\API\SettingsController;
use App\Http\Controllers\API\SubscriptionController;
use App\Http\Controllers\API\MarketingCampaignController;
use App\Http\Controllers\API\SustainabilityController;
use App\Http\Controllers\API\ExternalDataController;
use App\Http\Controllers\API\AffiliateController;
use App\Http\Controllers\API\AffiliateLinkController;
use App\Http\Controllers\API\AffiliateCommissionController;
use App\Http\Controllers\API\MessageController;
use App\Http\Controllers\API\ForumController;
use App\Http\Controllers\API\ForumPostController;
use App\Http\Controllers\API\FinancialReportController;
use App\Http\Controllers\API\BusinessVerificationController;
use App\Http\Controllers\Auth\TwoFactorAuthController;
use App\Http\Controllers\SecurityController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    // User routes
    Route::get('/user', [UserController::class, 'show']);
    Route::put('/user', [UserController::class, 'update']);
    Route::get('/user/profile', [UserController::class, 'profile']);
    Route::put('/user/profile', [UserController::class, 'updateProfile']);
    Route::get('/user/statistics', [UserController::class, 'statistics']);

    // Business Verification routes
    Route::get('/business-verification/status', [BusinessVerificationController::class, 'status']);
    Route::post('/business-verification', [BusinessVerificationController::class, 'store']);
    Route::get('/business-verification/{id}', [BusinessVerificationController::class, 'show']);

    // Product routes
    Route::apiResource('products', ProductController::class);

    // Contract routes
    Route::apiResource('contracts', ContractController::class);
    Route::post('/contracts/{contract}/sign', [ContractController::class, 'sign']);
    Route::get('/contracts/buyer', [ContractController::class, 'buyerContracts']);
    Route::get('/contracts/seller', [ContractController::class, 'sellerContracts']);

    // Shipment routes
    Route::apiResource('shipments', ShipmentController::class);
    Route::put('/shipments/{shipment}/status', [ShipmentController::class, 'updateStatus']);
    Route::post('/shipments/{shipment}/tracking', [ShipmentController::class, 'addTrackingUpdate']);
    Route::get('/shipments/track/{tracking_number}', [ShipmentController::class, 'track']);
    Route::post('/shipments/rates', [ShipmentController::class, 'getShippingRates']);
    Route::post('/shipments/customs-taxes', [ShipmentController::class, 'calculateCustomsTaxes']);
    Route::get('/shipments/customs-requirements/{country}', [ShipmentController::class, 'getCustomsRequirements']);

    // Market Analysis routes
    Route::get('/market-analysis', [MarketAnalysisController::class, 'index']);
    Route::get('/market-analysis/country/{country}', [MarketAnalysisController::class, 'byCountry']);
    Route::get('/market-analysis/forecast/{product_type}', [MarketAnalysisController::class, 'forecast']);
    Route::post('/market-analysis/recommendation', [MarketAnalysisController::class, 'getRecommendation']);
    Route::get('/market-analysis/{product_type}', [MarketAnalysisController::class, 'show']);

    // Advertisement routes
    Route::apiResource('advertisements', AdvertisementController::class);
    Route::get('/advertisements/featured', [AdvertisementController::class, 'featured']);
    Route::get('/advertisements/personalized', [AdvertisementController::class, 'personalized']);
    Route::get('/advertisements/related-to-product', [AdvertisementController::class, 'relatedToProduct']);
    Route::post('/advertisements/{id}/track-click', [AdvertisementController::class, 'trackClick']);
    Route::put('/advertisements/{advertisement}/approve', [AdvertisementController::class, 'approve']);
    Route::put('/advertisements/{advertisement}/reject', [AdvertisementController::class, 'reject']);
    Route::get('/moderator/advertisements', [AdvertisementController::class, 'moderatorReview']);

    // Marketing Campaign routes
    Route::apiResource('marketing-campaigns', MarketingCampaignController::class);
    Route::get('/marketing-campaigns/{campaign}/metrics', [MarketingCampaignController::class, 'metrics']);
    Route::post('/marketing-campaigns/{campaign}/metrics', [MarketingCampaignController::class, 'updateMetrics']);

    // Payment routes
    Route::apiResource('payments', PaymentController::class);
    Route::get('/payments/user', [PaymentController::class, 'getUserPayments']);
    Route::get('/payments/methods', [PaymentController::class, 'getPaymentMethods']);
    Route::post('/payments/methods', [PaymentController::class, 'addPaymentMethod']);
    Route::delete('/payments/methods/{method}', [PaymentController::class, 'removePaymentMethod']);
    Route::post('/payments/process', [PaymentController::class, 'processPayment']);
    Route::get('/payments/{payment}/status', [PaymentController::class, 'getPaymentStatus']);
    Route::get('/payments/{payment}/invoice', [PaymentController::class, 'getPaymentInvoice']);
    Route::post('/payments/{payment}/release', [PaymentController::class, 'releaseEscrowPayment']);
    Route::post('/payments/{payment}/dispute', [PaymentController::class, 'disputePayment']);
});

// Public market data
Route::get('/market-data/products', [MarketAnalysisController::class, 'publicProducts']);
Route::get('/market-data/trends', [MarketAnalysisController::class, 'publicTrends']);

// AI routes
Route::prefix('ai')->group(function () {
    Route::get('/price-prediction', [AIController::class, 'pricePrediction']);
    Route::get('/demand-supply', [AIController::class, 'demandSupply']);
    Route::get('/climate-impact', [AIController::class, 'climateImpact']);
    Route::get('/missing-products', [AIController::class, 'missingProducts']);
    Route::get('/seasonal-analysis', [AIController::class, 'seasonalAnalysis']);
    Route::get('/smart-pricing', [AIController::class, 'smartPricing']);
    Route::get('/recommendations', [AIController::class, 'recommendations']);
    Route::get('/country-analysis', [AIController::class, 'countryAnalysis']);

    // Sustainability AI routes
    Route::post('/sustainability-score', [AIController::class, 'sustainabilityScore']);
    Route::post('/carbon-footprint', [AIController::class, 'carbonFootprint']);
    Route::post('/water-usage', [AIController::class, 'waterUsage']);
});

// Sustainability routes
Route::prefix('sustainability')->middleware('auth:sanctum')->group(function () {
    // Sustainable Practices
    Route::get('/practices', [SustainabilityController::class, 'getPractices']);
    Route::get('/practices/{id}', [SustainabilityController::class, 'getPractice']);
    Route::post('/practices', [SustainabilityController::class, 'createPractice']);
    Route::put('/practices/{id}', [SustainabilityController::class, 'updatePractice']);
    Route::delete('/practices/{id}', [SustainabilityController::class, 'deletePractice']);

    // Environmental Certificates
    Route::get('/certificates', [SustainabilityController::class, 'getCertificates']);
    Route::get('/certificates/{id}', [SustainabilityController::class, 'getCertificate']);
    Route::post('/certificates', [SustainabilityController::class, 'createCertificate']);
    Route::put('/certificates/{id}', [SustainabilityController::class, 'updateCertificate']);
    Route::delete('/certificates/{id}', [SustainabilityController::class, 'deleteCertificate']);

    // Carbon Footprint
    Route::get('/carbon-footprint', [SustainabilityController::class, 'getCarbonFootprints']);
    Route::post('/carbon-footprint', [SustainabilityController::class, 'createCarbonFootprint']);
    Route::get('/carbon-reduction-targets', [SustainabilityController::class, 'getCarbonReductionTargets']);
    Route::post('/carbon-reduction-targets', [SustainabilityController::class, 'createCarbonReductionTarget']);
});

// External Data routes
Route::prefix('external')->middleware('auth:sanctum')->group(function () {
    // FAO Data
    Route::get('/fao', [ExternalDataController::class, 'getFAOData']);
    Route::get('/fao/crop-production', [ExternalDataController::class, 'getFAOCropProduction']);
    Route::get('/fao/sustainability', [ExternalDataController::class, 'getFAOSustainabilityIndicators']);

    // World Bank Data
    Route::get('/worldbank', [ExternalDataController::class, 'getWorldBankData']);
    Route::get('/worldbank/climate', [ExternalDataController::class, 'getWorldBankClimateData']);
    Route::get('/worldbank/agriculture', [ExternalDataController::class, 'getWorldBankAgriculturalData']);

    // OpenWeather Data
    Route::get('/openweather', [ExternalDataController::class, 'getOpenWeatherData']);
    Route::get('/openweather/forecast', [ExternalDataController::class, 'getOpenWeatherForecast']);

    // Google Maps Data
    Route::get('/googlemaps/geocoding', [ExternalDataController::class, 'getGoogleMapsGeocoding']);
    Route::get('/googlemaps/distance-matrix', [ExternalDataController::class, 'getGoogleMapsDistanceMatrix']);
});

// Admin routes
Route::prefix('admin')->middleware('auth:sanctum')->group(function () {
    Route::get('/dashboard-stats', [AdminController::class, 'dashboardStats']);
    Route::get('/users', [AdminController::class, 'users']);
    Route::put('/users/{user}/verify', [AdminController::class, 'verifyUser']);
    Route::get('/advertisements', [AdminController::class, 'advertisements']);
    Route::get('/system-metrics', [AdminController::class, 'systemMetrics']);

    // Business Verification routes for admin
    Route::get('/business-verifications', [BusinessVerificationController::class, 'index']);
    Route::put('/business-verifications/{id}/review', [BusinessVerificationController::class, 'review']);
});

// Financial Reports routes
Route::prefix('financial-reports')->middleware('auth:sanctum')->group(function () {
    Route::get('/income-expense', [FinancialReportController::class, 'getIncomeExpenseReport']);
    Route::get('/sales-statistics', [FinancialReportController::class, 'getSalesStatisticsReport']);
    Route::get('/supplier-customer', [FinancialReportController::class, 'getSupplierCustomerReport']);
});

// Moderator routes
Route::prefix('moderator')->middleware('auth:sanctum')->group(function () {
    Route::get('/dashboard-stats', [ModeratorController::class, 'dashboardStats']);
    Route::get('/products/pending', [ModeratorController::class, 'pendingProducts']);
    Route::put('/products/{product}/review', [ModeratorController::class, 'reviewProduct']);
    Route::get('/advertisements/pending', [ModeratorController::class, 'pendingAdvertisements']);
    Route::put('/advertisements/{advertisement}/review', [ModeratorController::class, 'reviewAdvertisement']);

    // Business Verification routes for moderator
    Route::get('/business-verifications', [BusinessVerificationController::class, 'index']);
    Route::put('/business-verifications/{id}/review', [BusinessVerificationController::class, 'review']);
});

// Settings routes
Route::prefix('settings')->middleware('auth:sanctum')->group(function () {
    Route::get('/', [SettingsController::class, 'index']);
    Route::put('/general', [SettingsController::class, 'updateSettings']);
    Route::put('/notifications', [SettingsController::class, 'updateNotificationPreferences']);
    Route::put('/profile', [SettingsController::class, 'updateProfile']);
    Route::put('/password', [SettingsController::class, 'changePassword']);
});

// Two-Factor Authentication routes
Route::prefix('2fa')->middleware('auth:sanctum')->group(function () {
    Route::post('/enable', [TwoFactorAuthController::class, 'enable']);
    Route::post('/confirm', [TwoFactorAuthController::class, 'confirm']);
    Route::post('/disable', [TwoFactorAuthController::class, 'disable']);
    Route::post('/verify', [TwoFactorAuthController::class, 'verify']);
});

// Security routes
Route::prefix('security')->middleware('auth:sanctum')->group(function () {
    Route::get('/logs', [SecurityController::class, 'getUserLogs']);

    // Admin only routes
    Route::middleware('role:admin')->group(function () {
        Route::get('/logs/all', [SecurityController::class, 'getAllLogs']);
        Route::get('/statistics', [SecurityController::class, 'getStatistics']);
    });
});

// Subscription routes
Route::prefix('subscriptions')->middleware('auth:sanctum')->group(function () {
    Route::get('/plans', [SubscriptionController::class, 'getPlans']);
    Route::get('/current', [SubscriptionController::class, 'getCurrentSubscription']);
    Route::post('/subscribe', [SubscriptionController::class, 'subscribe']);
    Route::post('/cancel', [SubscriptionController::class, 'cancel']);
    Route::post('/renew', [SubscriptionController::class, 'renew']);
    Route::get('/history', [SubscriptionController::class, 'getSubscriptionHistory']);
    Route::get('/usage', [SubscriptionController::class, 'getUsageStats']);
});

// Affiliate Marketing routes
Route::prefix('affiliates')->middleware('auth:sanctum')->group(function () {
    // Affiliate account management
    Route::get('/', [AffiliateController::class, 'index']);
    Route::post('/', [AffiliateController::class, 'store']);
    Route::get('/{id}', [AffiliateController::class, 'show']);
    Route::put('/{id}', [AffiliateController::class, 'update']);
    Route::get('/dashboard/stats', [AffiliateController::class, 'dashboard']);

    // Affiliate links management
    Route::get('/links', [AffiliateLinkController::class, 'index']);
    Route::post('/links', [AffiliateLinkController::class, 'store']);
    Route::get('/links/{id}', [AffiliateLinkController::class, 'show']);
    Route::put('/links/{id}', [AffiliateLinkController::class, 'update']);
    Route::delete('/links/{id}', [AffiliateLinkController::class, 'destroy']);

    // Affiliate commissions management
    Route::get('/commissions', [AffiliateCommissionController::class, 'index']);
    Route::get('/commissions/{id}', [AffiliateCommissionController::class, 'show']);
    Route::put('/commissions/{id}', [AffiliateCommissionController::class, 'update']);
    Route::get('/commissions/statistics', [AffiliateCommissionController::class, 'statistics']);
    Route::post('/commissions/payout', [AffiliateCommissionController::class, 'requestPayout']);
});

// Public affiliate tracking route
Route::get('/ref/{trackingCode}', [AffiliateLinkController::class, 'trackClick']);

// Messaging routes
Route::prefix('messages')->middleware('auth:sanctum')->group(function () {
    Route::get('/conversations', [MessageController::class, 'getConversations']);
    Route::get('/{userId}', [MessageController::class, 'getMessages']);
    Route::post('/', [MessageController::class, 'sendMessage']);
    Route::put('/{messageId}/read', [MessageController::class, 'markAsRead']);
    Route::delete('/{messageId}', [MessageController::class, 'deleteMessage']);
});

// Forum routes
Route::prefix('forums')->group(function () {
    // Public forum routes
    Route::get('/', [ForumController::class, 'index']);
    Route::get('/{slug}', [ForumController::class, 'show']);
    Route::get('/category/{category}', [ForumController::class, 'getByCategory']);

    // Protected forum routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/', [ForumController::class, 'store']);
        Route::put('/{id}', [ForumController::class, 'update']);
        Route::delete('/{id}', [ForumController::class, 'destroy']);

        // Forum posts
        Route::post('/posts', [ForumPostController::class, 'store']);
        Route::get('/posts/{id}', [ForumPostController::class, 'show']);
        Route::put('/posts/{id}', [ForumPostController::class, 'update']);
        Route::delete('/posts/{id}', [ForumPostController::class, 'destroy']);
        Route::post('/posts/{id}/like', [ForumPostController::class, 'like']);
        Route::get('/user/{userId}/posts', [ForumPostController::class, 'getByUser']);
    });
});
