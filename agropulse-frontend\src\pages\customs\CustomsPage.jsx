import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'
import { 
  DocumentTextIcon,
  GlobeAmericasIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  InformationCircleIcon,
  DocumentArrowDownIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'
import { CustomsService } from '../../services/customsService'

const CustomsPage = () => {
  const { t } = useTranslation()
  const [countries, setCountries] = useState([])
  const [selectedCountry, setSelectedCountry] = useState('')
  const [productType, setProductType] = useState('')
  const [customsInfo, setCustomsInfo] = useState(null)
  const [loading, setLoading] = useState(false)
  const [documents, setDocuments] = useState([])
  const [tariffCalculator, setTariffCalculator] = useState({
    value: '',
    weight: '',
    result: null
  })

  useEffect(() => {
    fetchCountries()
    fetchDocuments()
  }, [])

  const fetchCountries = async () => {
    try {
      const response = await CustomsService.getCountries()
      setCountries(response.data)
    } catch (error) {
      console.error('Error fetching countries:', error)
      toast.error(t('customs.errorFetchingCountries'))
    }
  }

  const fetchDocuments = async () => {
    try {
      const response = await CustomsService.getCustomsDocuments()
      setDocuments(response.data)
    } catch (error) {
      console.error('Error fetching documents:', error)
    }
  }

  const handleCountryProductSearch = async () => {
    if (!selectedCountry || !productType) {
      toast.error(t('customs.selectCountryAndProduct'))
      return
    }

    try {
      setLoading(true)
      const response = await CustomsService.getCustomsRequirements(selectedCountry, productType)
      setCustomsInfo(response.data)
    } catch (error) {
      console.error('Error fetching customs info:', error)
      toast.error(t('customs.errorFetchingInfo'))
    } finally {
      setLoading(false)
    }
  }

  const calculateTariff = async () => {
    if (!tariffCalculator.value || !tariffCalculator.weight || !selectedCountry || !productType) {
      toast.error(t('customs.fillAllFields'))
      return
    }

    try {
      const response = await CustomsService.calculateTariff({
        country: selectedCountry,
        product_type: productType,
        value: tariffCalculator.value,
        weight: tariffCalculator.weight
      })
      setTariffCalculator(prev => ({ ...prev, result: response.data }))
    } catch (error) {
      console.error('Error calculating tariff:', error)
      toast.error(t('customs.errorCalculatingTariff'))
    }
  }

  const getRequirementStatusIcon = (status) => {
    switch (status) {
      case 'required':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
      case 'optional':
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />
      case 'not_required':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status) => {
    const statusClasses = {
      required: 'bg-red-100 text-red-800',
      optional: 'bg-blue-100 text-blue-800',
      not_required: 'bg-green-100 text-green-800'
    }
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusClasses[status] || statusClasses.required}`}>
        {t(`customs.status.${status}`)}
      </span>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center space-x-4">
            <DocumentTextIcon className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t('customs.customsInformation')}
              </h1>
              <p className="text-gray-600">
                {t('customs.getCustomsRequirementsAndCalculateTariffs')}
              </p>
            </div>
          </div>
        </div>

        {/* Search Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {t('customs.searchCustomsRequirements')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('customs.destinationCountry')}
              </label>
              <select
                value={selectedCountry}
                onChange={(e) => setSelectedCountry(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">{t('customs.selectCountry')}</option>
                {countries.map((country) => (
                  <option key={country.code} value={country.code}>
                    {country.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('customs.productType')}
              </label>
              <select
                value={productType}
                onChange={(e) => setProductType(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">{t('customs.selectProductType')}</option>
                <option value="fruits">{t('products.fruits')}</option>
                <option value="vegetables">{t('products.vegetables')}</option>
                <option value="grains">{t('products.grains')}</option>
                <option value="dairy">{t('products.dairy')}</option>
                <option value="meat">{t('products.meat')}</option>
                <option value="seafood">{t('products.seafood')}</option>
                <option value="spices">{t('products.spices')}</option>
                <option value="beverages">{t('products.beverages')}</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={handleCountryProductSearch}
                disabled={loading}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <>
                    <MagnifyingGlassIcon className="h-5 w-5" />
                    <span>{t('customs.search')}</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Customs Information Results */}
        {customsInfo && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center space-x-3 mb-6">
              <GlobeAmericasIcon className="h-6 w-6 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">
                {t('customs.requirementsFor')} {customsInfo.country_name} - {customsInfo.product_type}
              </h2>
            </div>

            {/* General Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">{t('customs.generalInfo')}</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">{t('customs.hsCode')}:</span>
                    <span className="font-medium text-blue-900">{customsInfo.hs_code}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">{t('customs.baseTariffRate')}:</span>
                    <span className="font-medium text-blue-900">{customsInfo.base_tariff_rate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">{t('customs.vatRate')}:</span>
                    <span className="font-medium text-blue-900">{customsInfo.vat_rate}%</span>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="font-medium text-green-900 mb-2">{t('customs.importRestrictions')}</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-green-700">{t('customs.quotaLimits')}:</span>
                    <span className="font-medium text-green-900">
                      {customsInfo.quota_limits || t('customs.noLimits')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">{t('customs.seasonalRestrictions')}:</span>
                    <span className="font-medium text-green-900">
                      {customsInfo.seasonal_restrictions ? t('common.yes') : t('common.no')}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Required Documents */}
            <div className="mb-6">
              <h3 className="font-medium text-gray-900 mb-4">{t('customs.requiredDocuments')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {customsInfo.required_documents?.map((doc, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getRequirementStatusIcon(doc.status)}
                        <h4 className="font-medium text-gray-900">{doc.name}</h4>
                      </div>
                      {getStatusBadge(doc.status)}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{doc.description}</p>
                    {doc.validity_period && (
                      <p className="text-xs text-gray-500">
                        {t('customs.validFor')}: {doc.validity_period}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Special Requirements */}
            {customsInfo.special_requirements && customsInfo.special_requirements.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="font-medium text-yellow-900 mb-2">{t('customs.specialRequirements')}</h3>
                <ul className="list-disc list-inside space-y-1 text-sm text-yellow-800">
                  {customsInfo.special_requirements.map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Tariff Calculator */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {t('customs.tariffCalculator')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('customs.productValue')} (USD)
              </label>
              <input
                type="number"
                value={tariffCalculator.value}
                onChange={(e) => setTariffCalculator(prev => ({ ...prev, value: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="10000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('customs.weight')} (kg)
              </label>
              <input
                type="number"
                value={tariffCalculator.weight}
                onChange={(e) => setTariffCalculator(prev => ({ ...prev, weight: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="1000"
              />
            </div>

            <div className="md:col-span-2 flex items-end">
              <button
                onClick={calculateTariff}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                {t('customs.calculateTariff')}
              </button>
            </div>
          </div>

          {tariffCalculator.result && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">{t('customs.calculationResults')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">{t('customs.baseTariff')}:</span>
                  <p className="font-medium text-gray-900">${tariffCalculator.result.base_tariff}</p>
                </div>
                <div>
                  <span className="text-gray-600">{t('customs.vat')}:</span>
                  <p className="font-medium text-gray-900">${tariffCalculator.result.vat}</p>
                </div>
                <div>
                  <span className="text-gray-600">{t('customs.additionalFees')}:</span>
                  <p className="font-medium text-gray-900">${tariffCalculator.result.additional_fees}</p>
                </div>
                <div>
                  <span className="text-gray-600">{t('customs.totalCost')}:</span>
                  <p className="font-bold text-green-600 text-lg">${tariffCalculator.result.total_cost}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Customs Documents Library */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {t('customs.documentsLibrary')}
          </h2>
          
          {documents.length === 0 ? (
            <div className="text-center py-8">
              <DocumentTextIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('customs.noDocuments')}
              </h3>
              <p className="text-gray-500">
                {t('customs.noDocumentsDescription')}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {documents.map((doc) => (
                <div key={doc.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <DocumentTextIcon className="h-8 w-8 text-blue-600" />
                    <div>
                      <h3 className="font-medium text-gray-900">{doc.title}</h3>
                      <p className="text-sm text-gray-500">{doc.category}</p>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{doc.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {t('customs.lastUpdated')}: {new Date(doc.updated_at).toLocaleDateString()}
                    </span>
                    <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center space-x-1">
                      <DocumentArrowDownIcon className="h-4 w-4" />
                      <span>{t('customs.download')}</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CustomsPage
