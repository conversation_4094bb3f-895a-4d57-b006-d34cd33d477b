<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'cancellation_reason',
        'amount_paid',
        'payment_method',
        'stripe_subscription_id',
        'paypal_subscription_id',
        'usage_limits',
        'auto_renew',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'amount_paid' => 'decimal:2',
        'usage_limits' => 'array',
        'auto_renew' => 'boolean',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the subscription payments.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(SubscriptionPayment::class);
    }

    /**
     * Scope to get active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('starts_at', '<=', now())
                    ->where('ends_at', '>', now());
    }

    /**
     * Scope to get expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('ends_at', '<', now());
    }

    /**
     * Scope to get trial subscriptions.
     */
    public function scopeTrial($query)
    {
        return $query->where('status', 'trial')
                    ->where('trial_ends_at', '>', now());
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->starts_at <= now() && 
               $this->ends_at > now();
    }

    /**
     * Check if subscription is in trial period.
     */
    public function isInTrial(): bool
    {
        return $this->status === 'trial' && 
               $this->trial_ends_at && 
               $this->trial_ends_at > now();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->ends_at < now();
    }

    /**
     * Check if subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get days remaining in subscription.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return $this->ends_at->diffInDays(now());
    }

    /**
     * Get usage for specific resource.
     */
    public function getUsage(string $resource): int
    {
        return $this->usage_limits[$resource] ?? 0;
    }

    /**
     * Update usage for specific resource.
     */
    public function updateUsage(string $resource, int $amount = 1): void
    {
        $usage = $this->usage_limits ?? [];
        $usage[$resource] = ($usage[$resource] ?? 0) + $amount;
        $this->update(['usage_limits' => $usage]);
    }

    /**
     * Check if user can use specific resource.
     */
    public function canUse(string $resource): bool
    {
        if (!$this->isActive() && !$this->isInTrial()) {
            return false;
        }

        $limit = $this->subscriptionPlan->getLimit($resource);
        
        // Unlimited access
        if ($limit === null || $limit === -1) {
            return true;
        }

        $usage = $this->getUsage($resource);
        return $usage < $limit;
    }

    /**
     * Cancel subscription.
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'auto_renew' => false,
        ]);
    }

    /**
     * Renew subscription.
     */
    public function renew(int $days = null): void
    {
        $days = $days ?? $this->subscriptionPlan->duration_days;
        
        $this->update([
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addDays($days),
            'cancelled_at' => null,
            'cancellation_reason' => null,
        ]);
    }
}
