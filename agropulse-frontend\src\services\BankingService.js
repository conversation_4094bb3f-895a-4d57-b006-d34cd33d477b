import axios from 'axios';
import { API_BASE_URL } from '../config/constants';

const API_URL = `${API_BASE_URL}/banking`;

// Mock data for development
const USE_MOCK_DATA = true;

class BankingService {
  // Get user's bank accounts
  async getBankAccounts() {
    if (USE_MOCK_DATA) {
      return this.getMockBankAccounts();
    }

    try {
      const response = await axios.get(`${API_URL}/accounts`);
      return response.data;
    } catch (error) {
      console.error('Error fetching bank accounts:', error);
      throw error;
    }
  }

  // Add new bank account
  async addBankAccount(accountData) {
    if (USE_MOCK_DATA) {
      return this.getMockAddBankAccount(accountData);
    }

    try {
      const response = await axios.post(`${API_URL}/accounts`, accountData);
      return response.data;
    } catch (error) {
      console.error('Error adding bank account:', error);
      throw error;
    }
  }

  // Update bank account
  async updateBankAccount(accountId, accountData) {
    if (USE_MOCK_DATA) {
      return this.getMockUpdateBankAccount(accountId, accountData);
    }

    try {
      const response = await axios.put(`${API_URL}/accounts/${accountId}`, accountData);
      return response.data;
    } catch (error) {
      console.error('Error updating bank account:', error);
      throw error;
    }
  }

  // Delete bank account
  async deleteBankAccount(accountId) {
    if (USE_MOCK_DATA) {
      return this.getMockDeleteBankAccount(accountId);
    }

    try {
      const response = await axios.delete(`${API_URL}/accounts/${accountId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting bank account:', error);
      throw error;
    }
  }

  // Set primary account
  async setPrimaryAccount(accountId) {
    if (USE_MOCK_DATA) {
      return this.getMockSetPrimaryAccount(accountId);
    }

    try {
      const response = await axios.post(`${API_URL}/accounts/${accountId}/set-primary`);
      return response.data;
    } catch (error) {
      console.error('Error setting primary account:', error);
      throw error;
    }
  }

  // Get transactions
  async getTransactions(params = {}) {
    if (USE_MOCK_DATA) {
      return this.getMockTransactions(params);
    }

    try {
      const response = await axios.get(`${API_URL}/transactions`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  }

  // Initiate bank transfer
  async initiateTransfer(transferData) {
    if (USE_MOCK_DATA) {
      return this.getMockInitiateTransfer(transferData);
    }

    try {
      const response = await axios.post(`${API_URL}/transfer`, transferData);
      return response.data;
    } catch (error) {
      console.error('Error initiating transfer:', error);
      throw error;
    }
  }

  // Verify bank account
  async verifyAccount(accountId, verificationData) {
    if (USE_MOCK_DATA) {
      return this.getMockVerifyAccount(accountId, verificationData);
    }

    try {
      const response = await axios.post(`${API_URL}/accounts/${accountId}/verify`, verificationData);
      return response.data;
    } catch (error) {
      console.error('Error verifying account:', error);
      throw error;
    }
  }

  // Mock data methods
  getMockBankAccounts() {
    return {
      status: 'success',
      data: [
        {
          id: 1,
          bank_name: 'National Bank of Egypt',
          account_holder_name: 'Ahmed Mohamed',
          account_number: '****************',
          iban: '*****************************',
          swift_code: 'NBEGEGCX',
          branch_name: 'Cairo Main Branch',
          account_type: 'business',
          currency: 'EGP',
          is_primary: true,
          is_verified: true,
          balance: 125000.50,
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-20T14:45:00Z'
        },
        {
          id: 2,
          bank_name: 'Emirates NBD',
          account_holder_name: 'Ahmed Mohamed',
          account_number: '****************',
          iban: '***********************',
          swift_code: 'EBILAEAD',
          branch_name: 'Dubai Business Bay',
          account_type: 'checking',
          currency: 'AED',
          is_primary: false,
          is_verified: true,
          balance: 45000.00,
          created_at: '2024-01-10T09:15:00Z',
          updated_at: '2024-01-18T16:20:00Z'
        },
        {
          id: 3,
          bank_name: 'Al Rajhi Bank',
          account_holder_name: 'Ahmed Mohamed',
          account_number: '****************',
          iban: '***************************',
          swift_code: 'RJHISARI',
          branch_name: 'Riyadh King Fahd Road',
          account_type: 'savings',
          currency: 'SAR',
          is_primary: false,
          is_verified: false,
          balance: 75000.00,
          created_at: '2024-01-05T11:00:00Z',
          updated_at: '2024-01-12T13:30:00Z'
        }
      ]
    };
  }

  getMockAddBankAccount(accountData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Bank account added successfully',
          data: {
            id: Math.floor(Math.random() * 1000),
            ...accountData,
            is_verified: false,
            balance: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        });
      }, 1500);
    });
  }

  getMockUpdateBankAccount(accountId, accountData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Bank account updated successfully',
          data: {
            id: accountId,
            ...accountData,
            updated_at: new Date().toISOString()
          }
        });
      }, 1000);
    });
  }

  getMockDeleteBankAccount(accountId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Bank account deleted successfully'
        });
      }, 800);
    });
  }

  getMockSetPrimaryAccount(accountId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Primary account updated successfully'
        });
      }, 500);
    });
  }

  getMockTransactions(params) {
    const transactions = [
      {
        id: 1,
        description: 'Payment received from contract #1234',
        amount: 15000.00,
        currency: 'EGP',
        type: 'credit',
        status: 'completed',
        reference_number: 'TXN-2024-001',
        bank_account: {
          id: 1,
          bank_name: 'National Bank of Egypt',
          account_number: '****************'
        },
        created_at: '2024-01-20T14:30:00Z'
      },
      {
        id: 2,
        description: 'Subscription payment - Professional Plan',
        amount: 79.99,
        currency: 'USD',
        type: 'debit',
        status: 'completed',
        reference_number: 'TXN-2024-002',
        bank_account: {
          id: 2,
          bank_name: 'Emirates NBD',
          account_number: '****************'
        },
        created_at: '2024-01-18T10:15:00Z'
      },
      {
        id: 3,
        description: 'Withdrawal to external account',
        amount: 5000.00,
        currency: 'SAR',
        type: 'debit',
        status: 'pending',
        reference_number: 'TXN-2024-003',
        bank_account: {
          id: 3,
          bank_name: 'Al Rajhi Bank',
          account_number: '****************'
        },
        created_at: '2024-01-17T16:45:00Z'
      },
      {
        id: 4,
        description: 'Payment for shipment services',
        amount: 2500.00,
        currency: 'AED',
        type: 'debit',
        status: 'completed',
        reference_number: 'TXN-2024-004',
        bank_account: {
          id: 2,
          bank_name: 'Emirates NBD',
          account_number: '****************'
        },
        created_at: '2024-01-15T12:20:00Z'
      },
      {
        id: 5,
        description: 'Refund for cancelled order',
        amount: 1200.00,
        currency: 'EGP',
        type: 'credit',
        status: 'completed',
        reference_number: 'TXN-2024-005',
        bank_account: {
          id: 1,
          bank_name: 'National Bank of Egypt',
          account_number: '****************'
        },
        created_at: '2024-01-12T09:30:00Z'
      },
      {
        id: 6,
        description: 'Failed payment attempt',
        amount: 500.00,
        currency: 'USD',
        type: 'debit',
        status: 'failed',
        reference_number: 'TXN-2024-006',
        bank_account: {
          id: 2,
          bank_name: 'Emirates NBD',
          account_number: '****************'
        },
        created_at: '2024-01-10T15:10:00Z'
      }
    ];

    return {
      status: 'success',
      data: transactions
    };
  }

  getMockInitiateTransfer(transferData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const success = Math.random() > 0.1; // 90% success rate
        
        if (success) {
          resolve({
            status: 'success',
            message: 'Transfer initiated successfully',
            data: {
              id: Math.floor(Math.random() * 1000),
              reference_number: `TXN-${Date.now()}`,
              status: 'pending',
              ...transferData,
              created_at: new Date().toISOString()
            }
          });
        } else {
          resolve({
            status: 'error',
            message: 'Transfer failed due to insufficient funds'
          });
        }
      }, 2000);
    });
  }

  getMockVerifyAccount(accountId, verificationData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const success = Math.random() > 0.2; // 80% success rate
        
        if (success) {
          resolve({
            status: 'success',
            message: 'Account verified successfully',
            data: {
              account_id: accountId,
              is_verified: true,
              verified_at: new Date().toISOString()
            }
          });
        } else {
          resolve({
            status: 'error',
            message: 'Account verification failed. Please check your details and try again.'
          });
        }
      }, 3000);
    });
  }

  // Utility methods
  formatAccountNumber(accountNumber) {
    if (!accountNumber) return '';
    const length = accountNumber.length;
    if (length <= 4) return accountNumber;
    return '*'.repeat(length - 4) + accountNumber.slice(-4);
  }

  validateIBAN(iban) {
    // Basic IBAN validation
    const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/;
    return ibanRegex.test(iban.replace(/\s/g, ''));
  }

  validateSWIFT(swift) {
    // Basic SWIFT code validation
    const swiftRegex = /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
    return swiftRegex.test(swift);
  }

  getSupportedCurrencies() {
    return [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'EGP', name: 'Egyptian Pound', symbol: 'ج.م' },
      { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
      { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
      { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك' },
      { code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق' },
      { code: 'BHD', name: 'Bahraini Dinar', symbol: 'د.ب' },
      { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع' }
    ];
  }

  getAccountTypes() {
    return [
      { id: 'checking', name: 'Checking Account' },
      { id: 'savings', name: 'Savings Account' },
      { id: 'business', name: 'Business Account' },
      { id: 'investment', name: 'Investment Account' }
    ];
  }
}

export default new BankingService();
