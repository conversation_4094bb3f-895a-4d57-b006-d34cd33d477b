import axios from 'axios';
import { API_BASE_URL } from '../config/constants';

const API_URL = `${API_BASE_URL}/calendar`;

// Mock data for development
const USE_MOCK_DATA = true;

class CalendarService {
  // Get calendar events
  async getEvents(params = {}) {
    if (USE_MOCK_DATA) {
      return this.getMockEvents(params);
    }

    try {
      const response = await axios.get(`${API_URL}/events`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw error;
    }
  }

  // Create new event
  async createEvent(eventData) {
    if (USE_MOCK_DATA) {
      return this.getMockCreateEvent(eventData);
    }

    try {
      const response = await axios.post(`${API_URL}/events`, eventData);
      return response.data;
    } catch (error) {
      console.error('Error creating calendar event:', error);
      throw error;
    }
  }

  // Update event
  async updateEvent(eventId, eventData) {
    if (USE_MOCK_DATA) {
      return this.getMockUpdateEvent(eventId, eventData);
    }

    try {
      const response = await axios.put(`${API_URL}/events/${eventId}`, eventData);
      return response.data;
    } catch (error) {
      console.error('Error updating calendar event:', error);
      throw error;
    }
  }

  // Delete event
  async deleteEvent(eventId) {
    if (USE_MOCK_DATA) {
      return this.getMockDeleteEvent(eventId);
    }

    try {
      const response = await axios.delete(`${API_URL}/events/${eventId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting calendar event:', error);
      throw error;
    }
  }

  // Get seasonal data
  async getSeasonalData(params = {}) {
    if (USE_MOCK_DATA) {
      return this.getMockSeasonalData(params);
    }

    try {
      const response = await axios.get(`${API_URL}/seasonal`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching seasonal data:', error);
      throw error;
    }
  }

  // Get export/import seasons
  async getExportImportSeasons(params = {}) {
    if (USE_MOCK_DATA) {
      return this.getMockExportImportSeasons(params);
    }

    try {
      const response = await axios.get(`${API_URL}/export-import-seasons`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching export/import seasons:', error);
      throw error;
    }
  }

  // Mock data methods
  getMockEvents(params) {
    const currentDate = new Date();
    const year = params.year || currentDate.getFullYear();
    const month = params.month || currentDate.getMonth() + 1;

    const events = [
      // Planting events
      {
        id: 1,
        title: 'Wheat Planting Season Begins',
        description: 'Optimal time to start wheat planting in Egypt',
        date: `${year}-${month.toString().padStart(2, '0')}-05`,
        category: 'planting',
        product: 'wheat',
        country: 'egypt',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        title: 'Rice Planting Season',
        description: 'Rice planting season starts in the Nile Delta',
        date: `${year}-${month.toString().padStart(2, '0')}-10`,
        category: 'planting',
        product: 'rice',
        country: 'egypt',
        created_at: '2024-01-01T00:00:00Z'
      },
      
      // Harvesting events
      {
        id: 3,
        title: 'Tomato Harvest Peak',
        description: 'Peak harvesting season for tomatoes',
        date: `${year}-${month.toString().padStart(2, '0')}-15`,
        category: 'harvesting',
        product: 'tomatoes',
        country: 'morocco',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 4,
        title: 'Corn Harvest Season',
        description: 'Corn harvesting season in Saudi Arabia',
        date: `${year}-${month.toString().padStart(2, '0')}-20`,
        category: 'harvesting',
        product: 'corn',
        country: 'saudi',
        created_at: '2024-01-01T00:00:00Z'
      },
      
      // Export events
      {
        id: 5,
        title: 'Egypt Wheat Export Window Opens',
        description: 'Egypt opens wheat export window to Middle East',
        date: `${year}-${month.toString().padStart(2, '0')}-08`,
        category: 'export',
        product: 'wheat',
        country: 'egypt',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 6,
        title: 'Morocco Citrus Export Season',
        description: 'Morocco begins citrus export to Europe',
        date: `${year}-${month.toString().padStart(2, '0')}-12`,
        category: 'export',
        product: 'citrus',
        country: 'morocco',
        created_at: '2024-01-01T00:00:00Z'
      },
      
      // Import events
      {
        id: 7,
        title: 'UAE Rice Import Season',
        description: 'UAE increases rice imports for Ramadan season',
        date: `${year}-${month.toString().padStart(2, '0')}-18`,
        category: 'import',
        product: 'rice',
        country: 'uae',
        created_at: '2024-01-01T00:00:00Z'
      },
      
      // Weather events
      {
        id: 8,
        title: 'Monsoon Season Alert',
        description: 'Monsoon season affecting crop yields in South Asia',
        date: `${year}-${month.toString().padStart(2, '0')}-25`,
        category: 'weather',
        product: 'rice',
        country: 'india',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 9,
        title: 'Drought Warning',
        description: 'Drought conditions expected in North Africa',
        date: `${year}-${month.toString().padStart(2, '0')}-28`,
        category: 'weather',
        product: 'wheat',
        country: 'egypt',
        created_at: '2024-01-01T00:00:00Z'
      },
      
      // Treatment events
      {
        id: 10,
        title: 'Pest Control Treatment',
        description: 'Recommended pest control treatment for wheat crops',
        date: `${year}-${month.toString().padStart(2, '0')}-03`,
        category: 'treatment',
        product: 'wheat',
        country: 'egypt',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 11,
        title: 'Fertilizer Application',
        description: 'Optimal time for fertilizer application on corn',
        date: `${year}-${month.toString().padStart(2, '0')}-22`,
        category: 'treatment',
        product: 'corn',
        country: 'saudi',
        created_at: '2024-01-01T00:00:00Z'
      }
    ];

    return {
      status: 'success',
      data: events
    };
  }

  getMockCreateEvent(eventData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Event created successfully',
          data: {
            id: Math.floor(Math.random() * 1000),
            ...eventData,
            created_at: new Date().toISOString()
          }
        });
      }, 1000);
    });
  }

  getMockUpdateEvent(eventId, eventData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Event updated successfully',
          data: {
            id: eventId,
            ...eventData,
            updated_at: new Date().toISOString()
          }
        });
      }, 1000);
    });
  }

  getMockDeleteEvent(eventId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          message: 'Event deleted successfully'
        });
      }, 500);
    });
  }

  getMockSeasonalData(params) {
    const seasonalData = [
      {
        product: 'wheat',
        country: 'egypt',
        planting_season: {
          start: 'November',
          end: 'December',
          optimal_conditions: 'Cool, dry weather'
        },
        harvesting_season: {
          start: 'April',
          end: 'May',
          expected_yield: '3.2 tons/hectare'
        },
        export_window: {
          start: 'June',
          end: 'August',
          main_destinations: ['Saudi Arabia', 'UAE', 'Jordan']
        }
      },
      {
        product: 'rice',
        country: 'egypt',
        planting_season: {
          start: 'May',
          end: 'June',
          optimal_conditions: 'Warm, humid weather'
        },
        harvesting_season: {
          start: 'September',
          end: 'October',
          expected_yield: '4.1 tons/hectare'
        },
        export_window: {
          start: 'November',
          end: 'January',
          main_destinations: ['Libya', 'Sudan', 'Lebanon']
        }
      },
      {
        product: 'tomatoes',
        country: 'morocco',
        planting_season: {
          start: 'September',
          end: 'October',
          optimal_conditions: 'Mild temperatures, low humidity'
        },
        harvesting_season: {
          start: 'December',
          end: 'March',
          expected_yield: '45 tons/hectare'
        },
        export_window: {
          start: 'January',
          end: 'April',
          main_destinations: ['France', 'Spain', 'Germany']
        }
      },
      {
        product: 'corn',
        country: 'saudi',
        planting_season: {
          start: 'March',
          end: 'April',
          optimal_conditions: 'Warm weather, adequate irrigation'
        },
        harvesting_season: {
          start: 'July',
          end: 'August',
          expected_yield: '8.5 tons/hectare'
        },
        export_window: {
          start: 'September',
          end: 'November',
          main_destinations: ['UAE', 'Kuwait', 'Qatar']
        }
      }
    ];

    return {
      status: 'success',
      data: seasonalData
    };
  }

  getMockExportImportSeasons(params) {
    const exportImportData = [
      {
        country: 'egypt',
        exports: [
          {
            product: 'wheat',
            season: 'June - August',
            peak_month: 'July',
            volume: '2.1 million tons',
            main_destinations: ['Saudi Arabia', 'UAE', 'Jordan']
          },
          {
            product: 'rice',
            season: 'November - January',
            peak_month: 'December',
            volume: '1.8 million tons',
            main_destinations: ['Libya', 'Sudan', 'Lebanon']
          }
        ],
        imports: [
          {
            product: 'soybeans',
            season: 'March - May',
            peak_month: 'April',
            volume: '3.2 million tons',
            main_sources: ['Brazil', 'Argentina', 'USA']
          }
        ]
      },
      {
        country: 'saudi',
        exports: [
          {
            product: 'dates',
            season: 'September - November',
            peak_month: 'October',
            volume: '1.3 million tons',
            main_destinations: ['India', 'Pakistan', 'Indonesia']
          }
        ],
        imports: [
          {
            product: 'wheat',
            season: 'Year-round',
            peak_month: 'August',
            volume: '3.5 million tons',
            main_sources: ['Russia', 'Ukraine', 'Australia']
          },
          {
            product: 'rice',
            season: 'Year-round',
            peak_month: 'March',
            volume: '1.2 million tons',
            main_sources: ['India', 'Pakistan', 'Thailand']
          }
        ]
      },
      {
        country: 'morocco',
        exports: [
          {
            product: 'citrus',
            season: 'November - April',
            peak_month: 'February',
            volume: '2.8 million tons',
            main_destinations: ['Russia', 'EU', 'Canada']
          },
          {
            product: 'tomatoes',
            season: 'December - May',
            peak_month: 'March',
            volume: '1.9 million tons',
            main_destinations: ['France', 'Spain', 'Germany']
          }
        ],
        imports: [
          {
            product: 'wheat',
            season: 'Year-round',
            peak_month: 'June',
            volume: '4.1 million tons',
            main_sources: ['France', 'Ukraine', 'Russia']
          }
        ]
      },
      {
        country: 'uae',
        exports: [
          {
            product: 'dates',
            season: 'September - December',
            peak_month: 'November',
            volume: '0.8 million tons',
            main_destinations: ['India', 'Bangladesh', 'Sri Lanka']
          }
        ],
        imports: [
          {
            product: 'rice',
            season: 'Year-round',
            peak_month: 'March',
            volume: '2.1 million tons',
            main_sources: ['India', 'Pakistan', 'Thailand']
          },
          {
            product: 'wheat',
            season: 'Year-round',
            peak_month: 'July',
            volume: '1.8 million tons',
            main_sources: ['Australia', 'Russia', 'Ukraine']
          }
        ]
      }
    ];

    return {
      status: 'success',
      data: exportImportData
    };
  }
}

export default new CalendarService();
