import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import {
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  SunIcon,
  CloudIcon,
  BeakerIcon,
  TruckIcon
} from '@heroicons/react/24/outline';
import { CalendarService } from '../../services/CalendarService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

const AgriculturalCalendar = () => {
  const { t } = useTranslation();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState('month'); // month, week, day
  const [events, setEvents] = useState([]);
  const [filteredEvents, setFilteredEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [filters, setFilters] = useState({
    category: 'all',
    product: 'all',
    country: 'all'
  });

  const eventCategories = [
    { id: 'planting', name: t('calendar.planting'), color: 'bg-green-500', icon: SunIcon },
    { id: 'harvesting', name: t('calendar.harvesting'), color: 'bg-yellow-500', icon: SunIcon },
    { id: 'export', name: t('calendar.export'), color: 'bg-blue-500', icon: TruckIcon },
    { id: 'import', name: t('calendar.import'), color: 'bg-purple-500', icon: TruckIcon },
    { id: 'weather', name: t('calendar.weather'), color: 'bg-gray-500', icon: CloudIcon },
    { id: 'treatment', name: t('calendar.treatment'), color: 'bg-red-500', icon: BeakerIcon }
  ];

  useEffect(() => {
    fetchEvents();
  }, [currentDate]);

  useEffect(() => {
    applyFilters();
  }, [events, filters]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await CalendarService.getEvents({
        year: currentDate.getFullYear(),
        month: currentDate.getMonth() + 1
      });
      setEvents(response.data);
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      toast.error(t('calendar.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = events;

    if (filters.category !== 'all') {
      filtered = filtered.filter(event => event.category === filters.category);
    }

    if (filters.product !== 'all') {
      filtered = filtered.filter(event => event.product === filters.product);
    }

    if (filters.country !== 'all') {
      filtered = filtered.filter(event => event.country === filters.country);
    }

    setFilteredEvents(filtered);
  };

  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const getEventsForDate = (date) => {
    if (!date) return [];
    
    return filteredEvents.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate.toDateString() === date.toDateString();
    });
  };

  const getCategoryConfig = (category) => {
    return eventCategories.find(cat => cat.id === category) || eventCategories[0];
  };

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const monthNames = [
    t('calendar.months.january'),
    t('calendar.months.february'),
    t('calendar.months.march'),
    t('calendar.months.april'),
    t('calendar.months.may'),
    t('calendar.months.june'),
    t('calendar.months.july'),
    t('calendar.months.august'),
    t('calendar.months.september'),
    t('calendar.months.october'),
    t('calendar.months.november'),
    t('calendar.months.december')
  ];

  const dayNames = [
    t('calendar.days.sunday'),
    t('calendar.days.monday'),
    t('calendar.days.tuesday'),
    t('calendar.days.wednesday'),
    t('calendar.days.thursday'),
    t('calendar.days.friday'),
    t('calendar.days.saturday')
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  const days = getDaysInMonth(currentDate);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <CalendarIcon className="h-8 w-8 mr-3 text-green-600" />
              {t('calendar.title')}
            </h1>
            <p className="mt-2 text-gray-600">
              {t('calendar.description')}
            </p>
          </div>
          
          <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
            <PlusIcon className="h-4 w-4 mr-2" />
            {t('calendar.addEvent')}
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg mb-6 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('calendar.category')}
            </label>
            <select
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="all">{t('calendar.allCategories')}</option>
              {eventCategories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('calendar.product')}
            </label>
            <select
              value={filters.product}
              onChange={(e) => setFilters(prev => ({ ...prev, product: e.target.value }))}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="all">{t('calendar.allProducts')}</option>
              <option value="wheat">{t('products.wheat')}</option>
              <option value="rice">{t('products.rice')}</option>
              <option value="corn">{t('products.corn')}</option>
              <option value="tomatoes">{t('products.tomatoes')}</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('calendar.country')}
            </label>
            <select
              value={filters.country}
              onChange={(e) => setFilters(prev => ({ ...prev, country: e.target.value }))}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="all">{t('calendar.allCountries')}</option>
              <option value="egypt">{t('countries.egypt')}</option>
              <option value="saudi">{t('countries.saudi')}</option>
              <option value="uae">{t('countries.uae')}</option>
              <option value="morocco">{t('countries.morocco')}</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('calendar.view')}
            </label>
            <div className="flex rounded-md shadow-sm">
              <button
                onClick={() => setView('month')}
                className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                  view === 'month'
                    ? 'bg-green-600 text-white border-green-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {t('calendar.month')}
              </button>
              <button
                onClick={() => setView('week')}
                className={`px-3 py-2 text-sm font-medium border-t border-b ${
                  view === 'week'
                    ? 'bg-green-600 text-white border-green-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {t('calendar.week')}
              </button>
              <button
                onClick={() => setView('day')}
                className={`px-3 py-2 text-sm font-medium rounded-r-md border ${
                  view === 'day'
                    ? 'bg-green-600 text-white border-green-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {t('calendar.day')}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {/* Calendar Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </h2>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigateMonth(-1)}
                className="p-2 rounded-md hover:bg-gray-100"
              >
                <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
              </button>
              
              <button
                onClick={() => setCurrentDate(new Date())}
                className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                {t('calendar.today')}
              </button>
              
              <button
                onClick={() => navigateMonth(1)}
                className="p-2 rounded-md hover:bg-gray-100"
              >
                <ChevronRightIcon className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-0 border-b border-gray-200">
          {dayNames.map((day, index) => (
            <div key={index} className="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide bg-gray-50 border-r border-gray-200 last:border-r-0">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-0">
          {days.map((day, index) => {
            const dayEvents = day ? getEventsForDate(day) : [];
            const isToday = day && day.toDateString() === new Date().toDateString();
            const isCurrentMonth = day && day.getMonth() === currentDate.getMonth();

            return (
              <div
                key={index}
                className={`min-h-[120px] border-r border-b border-gray-200 last:border-r-0 p-2 ${
                  !isCurrentMonth ? 'bg-gray-50' : 'bg-white'
                }`}
              >
                {day && (
                  <>
                    <div className={`text-sm font-medium mb-1 ${
                      isToday 
                        ? 'bg-green-600 text-white w-6 h-6 rounded-full flex items-center justify-center'
                        : isCurrentMonth 
                          ? 'text-gray-900' 
                          : 'text-gray-400'
                    }`}>
                      {day.getDate()}
                    </div>
                    
                    <div className="space-y-1">
                      {dayEvents.slice(0, 3).map((event, eventIndex) => {
                        const categoryConfig = getCategoryConfig(event.category);
                        const Icon = categoryConfig.icon;
                        
                        return (
                          <div
                            key={eventIndex}
                            onClick={() => handleEventClick(event)}
                            className={`text-xs p-1 rounded cursor-pointer hover:opacity-80 ${categoryConfig.color} text-white`}
                          >
                            <div className="flex items-center">
                              <Icon className="h-3 w-3 mr-1" />
                              <span className="truncate">{event.title}</span>
                            </div>
                          </div>
                        );
                      })}
                      
                      {dayEvents.length > 3 && (
                        <div className="text-xs text-gray-500 font-medium">
                          +{dayEvents.length - 3} {t('calendar.more')}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="mt-6 bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {t('calendar.legend')}
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {eventCategories.map(category => {
            const Icon = category.icon;
            return (
              <div key={category.id} className="flex items-center">
                <div className={`w-4 h-4 rounded ${category.color} mr-2 flex items-center justify-center`}>
                  <Icon className="h-3 w-3 text-white" />
                </div>
                <span className="text-sm text-gray-700">{category.name}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Event Modal */}
      {showEventModal && selectedEvent && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {selectedEvent.title}
                </h3>
                <button
                  onClick={() => setShowEventModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">{t('common.close')}</span>
                  ×
                </button>
              </div>
              
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">
                    {t('calendar.category')}:
                  </span>
                  <span className="ml-2 text-sm text-gray-900">
                    {getCategoryConfig(selectedEvent.category).name}
                  </span>
                </div>
                
                <div>
                  <span className="text-sm font-medium text-gray-500">
                    {t('calendar.date')}:
                  </span>
                  <span className="ml-2 text-sm text-gray-900">
                    {new Date(selectedEvent.date).toLocaleDateString()}
                  </span>
                </div>
                
                {selectedEvent.product && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">
                      {t('calendar.product')}:
                    </span>
                    <span className="ml-2 text-sm text-gray-900">
                      {selectedEvent.product}
                    </span>
                  </div>
                )}
                
                {selectedEvent.country && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">
                      {t('calendar.country')}:
                    </span>
                    <span className="ml-2 text-sm text-gray-900">
                      {selectedEvent.country}
                    </span>
                  </div>
                )}
                
                {selectedEvent.description && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">
                      {t('calendar.description')}:
                    </span>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedEvent.description}
                    </p>
                  </div>
                )}
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowEventModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  {t('common.close')}
                </button>
                
                <button className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                  <PencilIcon className="h-4 w-4 inline mr-1" />
                  {t('common.edit')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgriculturalCalendar;
