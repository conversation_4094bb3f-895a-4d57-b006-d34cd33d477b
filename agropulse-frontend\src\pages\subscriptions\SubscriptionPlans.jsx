import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  CheckIcon,
  XMarkIcon,
  StarIcon,
  CreditCardIcon,
  BanknotesIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { SubscriptionService } from '../../services/SubscriptionService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

const SubscriptionPlans = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [plans, setPlans] = useState([]);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState(null);
  const [billingCycle, setBillingCycle] = useState('monthly');

  useEffect(() => {
    fetchPlans();
    fetchCurrentSubscription();
  }, []);

  const fetchPlans = async () => {
    try {
      const response = await SubscriptionService.getPlans();
      setPlans(response.data);
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast.error(t('subscriptions.fetchPlansError'));
    }
  };

  const fetchCurrentSubscription = async () => {
    try {
      const response = await SubscriptionService.getCurrentSubscription();
      setCurrentSubscription(response.data);
    } catch (error) {
      console.error('Error fetching current subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (planId) => {
    setSubscribing(planId);
    try {
      // Navigate to payment page with plan ID
      navigate(`/dashboard/subscriptions/checkout/${planId}`);
    } catch (error) {
      console.error('Error initiating subscription:', error);
      toast.error(t('subscriptions.subscribeError'));
    } finally {
      setSubscribing(null);
    }
  };

  const filteredPlans = plans.filter(plan => plan.billing_cycle === billingCycle);

  const getPlanFeatures = (features) => {
    const featureLabels = {
      'unlimited_products': t('subscriptions.features.unlimitedProducts'),
      'unlimited_contracts': t('subscriptions.features.unlimitedContracts'),
      'ai_analytics': t('subscriptions.features.aiAnalytics'),
      'priority_support': t('subscriptions.features.prioritySupport'),
      'advanced_reports': t('subscriptions.features.advancedReports'),
      'custom_branding': t('subscriptions.features.customBranding'),
      'api_access': t('subscriptions.features.apiAccess'),
      'white_label': t('subscriptions.features.whiteLabel'),
      'dedicated_manager': t('subscriptions.features.dedicatedManager'),
      'sla_guarantee': t('subscriptions.features.slaGuarantee')
    };

    return features.map(feature => featureLabels[feature] || feature);
  };

  const getLimitDisplay = (limits, resource) => {
    const limit = limits[resource];
    if (limit === -1 || limit === null) {
      return t('subscriptions.unlimited');
    }
    return limit.toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            {t('subscriptions.choosePlan')}
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            {t('subscriptions.choosePlanDescription')}
          </p>
        </div>

        {/* Current Subscription Alert */}
        {currentSubscription && (
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <ShieldCheckIcon className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  {t('subscriptions.currentPlan')}
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    {t('subscriptions.currentPlanDetails', {
                      plan: currentSubscription.subscription?.subscriptionPlan?.name,
                      daysRemaining: currentSubscription.days_remaining
                    })}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Billing Cycle Toggle */}
        <div className="mt-12 flex justify-center">
          <div className="relative bg-gray-100 p-1 rounded-lg">
            <div className="relative flex">
              <button
                type="button"
                className={`relative px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  billingCycle === 'monthly'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setBillingCycle('monthly')}
              >
                {t('subscriptions.monthly')}
              </button>
              <button
                type="button"
                className={`relative px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  billingCycle === 'yearly'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setBillingCycle('yearly')}
              >
                {t('subscriptions.yearly')}
                <span className="ml-1 text-xs text-green-600 font-semibold">
                  {t('subscriptions.save20')}
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0 xl:grid-cols-3">
          {filteredPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative border rounded-lg shadow-sm divide-y divide-gray-200 ${
                plan.is_popular
                  ? 'border-green-500 shadow-lg'
                  : 'border-gray-200'
              }`}
            >
              {plan.is_popular && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
                    <StarIcon className="h-3 w-3 mr-1" />
                    {t('subscriptions.popular')}
                  </span>
                </div>
              )}

              <div className="p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {plan.name}
                </h3>
                <p className="mt-4 text-sm text-gray-500">
                  {plan.description}
                </p>
                <p className="mt-8">
                  <span className="text-4xl font-extrabold text-gray-900">
                    ${plan.price}
                  </span>
                  <span className="text-base font-medium text-gray-500">
                    /{plan.billing_cycle_label.toLowerCase()}
                  </span>
                </p>
                {plan.trial_days > 0 && (
                  <p className="mt-2 text-sm text-green-600">
                    {t('subscriptions.freeTrial', { days: plan.trial_days })}
                  </p>
                )}
                <button
                  type="button"
                  className={`mt-8 block w-full py-3 px-6 border border-transparent rounded-md text-center font-medium transition-colors ${
                    plan.is_popular
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-800 text-white hover:bg-gray-900'
                  } ${subscribing === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={subscribing === plan.id || (currentSubscription && currentSubscription.subscription?.subscription_plan_id === plan.id)}
                >
                  {subscribing === plan.id ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {t('subscriptions.processing')}
                    </div>
                  ) : currentSubscription && currentSubscription.subscription?.subscription_plan_id === plan.id ? (
                    t('subscriptions.currentPlan')
                  ) : (
                    t('subscriptions.subscribe')
                  )}
                </button>
              </div>

              <div className="pt-6 pb-8 px-6">
                <h4 className="text-sm font-medium text-gray-900 tracking-wide uppercase">
                  {t('subscriptions.features')}
                </h4>
                <ul className="mt-6 space-y-4">
                  {getPlanFeatures(plan.features).map((feature, index) => (
                    <li key={index} className="flex space-x-3">
                      <CheckIcon className="flex-shrink-0 h-5 w-5 text-green-500" />
                      <span className="text-sm text-gray-500">{feature}</span>
                    </li>
                  ))}
                </ul>

                <h4 className="mt-8 text-sm font-medium text-gray-900 tracking-wide uppercase">
                  {t('subscriptions.limits')}
                </h4>
                <ul className="mt-6 space-y-4">
                  <li className="flex justify-between">
                    <span className="text-sm text-gray-500">{t('subscriptions.products')}</span>
                    <span className="text-sm font-medium text-gray-900">
                      {getLimitDisplay(plan.limits, 'products')}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-sm text-gray-500">{t('subscriptions.contracts')}</span>
                    <span className="text-sm font-medium text-gray-900">
                      {getLimitDisplay(plan.limits, 'contracts')}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-sm text-gray-500">{t('subscriptions.advertisements')}</span>
                    <span className="text-sm font-medium text-gray-900">
                      {getLimitDisplay(plan.limits, 'advertisements')}
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Payment Methods */}
        <div className="mt-16 text-center">
          <h3 className="text-lg font-medium text-gray-900">
            {t('subscriptions.acceptedPayments')}
          </h3>
          <div className="mt-4 flex justify-center space-x-6">
            <div className="flex items-center">
              <CreditCardIcon className="h-6 w-6 text-gray-400 mr-2" />
              <span className="text-sm text-gray-500">{t('subscriptions.creditCard')}</span>
            </div>
            <div className="flex items-center">
              <BanknotesIcon className="h-6 w-6 text-gray-400 mr-2" />
              <span className="text-sm text-gray-500">{t('subscriptions.paypal')}</span>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h3 className="text-lg font-medium text-gray-900 text-center mb-8">
            {t('subscriptions.faq')}
          </h3>
          <div className="max-w-3xl mx-auto">
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-gray-900">{t('subscriptions.faq1Question')}</h4>
                <p className="mt-2 text-sm text-gray-500">{t('subscriptions.faq1Answer')}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{t('subscriptions.faq2Question')}</h4>
                <p className="mt-2 text-sm text-gray-500">{t('subscriptions.faq2Answer')}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{t('subscriptions.faq3Question')}</h4>
                <p className="mt-2 text-sm text-gray-500">{t('subscriptions.faq3Answer')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlans;
