import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

class DisputeService {
  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/disputes`,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })
  }

  // Get all disputes with filters
  async getDisputes(filters = {}) {
    try {
      const response = await this.api.get('/', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching disputes:', error)
      throw error
    }
  }

  // Get dispute details
  async getDisputeDetails(disputeId) {
    try {
      const response = await this.api.get(`/${disputeId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching dispute details:', error)
      throw error
    }
  }

  // Create new dispute
  async createDispute(disputeData) {
    try {
      const response = await this.api.post('/', disputeData)
      return response.data
    } catch (error) {
      console.error('Error creating dispute:', error)
      throw error
    }
  }

  // Update dispute
  async updateDispute(disputeId, updateData) {
    try {
      const response = await this.api.put(`/${disputeId}`, updateData)
      return response.data
    } catch (error) {
      console.error('Error updating dispute:', error)
      throw error
    }
  }

  // Close dispute
  async closeDispute(disputeId, resolution) {
    try {
      const response = await this.api.post(`/${disputeId}/close`, { resolution })
      return response.data
    } catch (error) {
      console.error('Error closing dispute:', error)
      throw error
    }
  }

  // Reopen dispute
  async reopenDispute(disputeId, reason) {
    try {
      const response = await this.api.post(`/${disputeId}/reopen`, { reason })
      return response.data
    } catch (error) {
      console.error('Error reopening dispute:', error)
      throw error
    }
  }

  // Get dispute messages/comments
  async getDisputeMessages(disputeId) {
    try {
      const response = await this.api.get(`/${disputeId}/messages`)
      return response.data
    } catch (error) {
      console.error('Error fetching dispute messages:', error)
      throw error
    }
  }

  // Add message to dispute
  async addDisputeMessage(disputeId, messageData) {
    try {
      const response = await this.api.post(`/${disputeId}/messages`, messageData)
      return response.data
    } catch (error) {
      console.error('Error adding dispute message:', error)
      throw error
    }
  }

  // Upload dispute attachment
  async uploadAttachment(disputeId, file) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await this.api.post(`/${disputeId}/attachments`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      return response.data
    } catch (error) {
      console.error('Error uploading attachment:', error)
      throw error
    }
  }

  // Get dispute attachments
  async getDisputeAttachments(disputeId) {
    try {
      const response = await this.api.get(`/${disputeId}/attachments`)
      return response.data
    } catch (error) {
      console.error('Error fetching dispute attachments:', error)
      throw error
    }
  }

  // Download attachment
  async downloadAttachment(disputeId, attachmentId) {
    try {
      const response = await this.api.get(`/${disputeId}/attachments/${attachmentId}/download`, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error downloading attachment:', error)
      throw error
    }
  }

  // Escalate dispute
  async escalateDispute(disputeId, escalationData) {
    try {
      const response = await this.api.post(`/${disputeId}/escalate`, escalationData)
      return response.data
    } catch (error) {
      console.error('Error escalating dispute:', error)
      throw error
    }
  }

  // Get dispute timeline
  async getDisputeTimeline(disputeId) {
    try {
      const response = await this.api.get(`/${disputeId}/timeline`)
      return response.data
    } catch (error) {
      console.error('Error fetching dispute timeline:', error)
      throw error
    }
  }

  // Assign dispute to moderator (admin only)
  async assignDispute(disputeId, moderatorId) {
    try {
      const response = await this.api.post(`/${disputeId}/assign`, { moderator_id: moderatorId })
      return response.data
    } catch (error) {
      console.error('Error assigning dispute:', error)
      throw error
    }
  }

  // Get dispute statistics
  async getDisputeStatistics(filters = {}) {
    try {
      const response = await this.api.get('/statistics', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching dispute statistics:', error)
      throw error
    }
  }

  // Get dispute categories
  async getDisputeCategories() {
    try {
      const response = await this.api.get('/categories')
      return response.data
    } catch (error) {
      console.error('Error fetching dispute categories:', error)
      throw error
    }
  }

  // Get dispute resolution templates
  async getResolutionTemplates() {
    try {
      const response = await this.api.get('/resolution-templates')
      return response.data
    } catch (error) {
      console.error('Error fetching resolution templates:', error)
      throw error
    }
  }

  // Submit dispute resolution
  async submitResolution(disputeId, resolutionData) {
    try {
      const response = await this.api.post(`/${disputeId}/resolution`, resolutionData)
      return response.data
    } catch (error) {
      console.error('Error submitting resolution:', error)
      throw error
    }
  }

  // Accept dispute resolution
  async acceptResolution(disputeId) {
    try {
      const response = await this.api.post(`/${disputeId}/resolution/accept`)
      return response.data
    } catch (error) {
      console.error('Error accepting resolution:', error)
      throw error
    }
  }

  // Reject dispute resolution
  async rejectResolution(disputeId, reason) {
    try {
      const response = await this.api.post(`/${disputeId}/resolution/reject`, { reason })
      return response.data
    } catch (error) {
      console.error('Error rejecting resolution:', error)
      throw error
    }
  }

  // Get dispute notifications
  async getDisputeNotifications() {
    try {
      const response = await this.api.get('/notifications')
      return response.data
    } catch (error) {
      console.error('Error fetching dispute notifications:', error)
      throw error
    }
  }

  // Mark notification as read
  async markNotificationAsRead(notificationId) {
    try {
      const response = await this.api.put(`/notifications/${notificationId}/read`)
      return response.data
    } catch (error) {
      console.error('Error marking notification as read:', error)
      throw error
    }
  }

  // Get dispute FAQ
  async getDisputeFAQ() {
    try {
      const response = await this.api.get('/faq')
      return response.data
    } catch (error) {
      console.error('Error fetching dispute FAQ:', error)
      throw error
    }
  }

  // Submit dispute feedback
  async submitFeedback(disputeId, feedbackData) {
    try {
      const response = await this.api.post(`/${disputeId}/feedback`, feedbackData)
      return response.data
    } catch (error) {
      console.error('Error submitting feedback:', error)
      throw error
    }
  }

  // Get dispute reports (admin only)
  async getDisputeReports(filters = {}) {
    try {
      const response = await this.api.get('/reports', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching dispute reports:', error)
      throw error
    }
  }

  // Export dispute data
  async exportDisputes(filters = {}) {
    try {
      const response = await this.api.get('/export', {
        params: filters,
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error exporting disputes:', error)
      throw error
    }
  }

  // Get dispute trends
  async getDisputeTrends(period = '30d') {
    try {
      const response = await this.api.get('/trends', { params: { period } })
      return response.data
    } catch (error) {
      console.error('Error fetching dispute trends:', error)
      throw error
    }
  }

  // Get similar disputes
  async getSimilarDisputes(disputeId) {
    try {
      const response = await this.api.get(`/${disputeId}/similar`)
      return response.data
    } catch (error) {
      console.error('Error fetching similar disputes:', error)
      throw error
    }
  }

  // Rate dispute resolution
  async rateResolution(disputeId, rating, comment = '') {
    try {
      const response = await this.api.post(`/${disputeId}/rate`, { rating, comment })
      return response.data
    } catch (error) {
      console.error('Error rating resolution:', error)
      throw error
    }
  }
}

export const disputeService = new DisputeService()
export { DisputeService }
