import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'
import { 
  BanknotesIcon,
  CreditCardIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { BankingService } from '../../services/bankingService'

const BankAccount = () => {
  const { t } = useTranslation()
  const [accounts, setAccounts] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddAccountModal, setShowAddAccountModal] = useState(false)
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false)
  const [selectedAccount, setSelectedAccount] = useState(null)
  const [balance, setBalance] = useState({
    total: 0,
    available: 0,
    pending: 0,
    currency: 'USD'
  })

  useEffect(() => {
    fetchBankingData()
  }, [])

  const fetchBankingData = async () => {
    try {
      setLoading(true)
      const [accountsRes, paymentMethodsRes, balanceRes] = await Promise.all([
        BankingService.getBankAccounts(),
        BankingService.getPaymentMethods(),
        BankingService.getBalance()
      ])
      
      setAccounts(accountsRes.data)
      setPaymentMethods(paymentMethodsRes.data)
      setBalance(balanceRes.data)
    } catch (error) {
      console.error('Error fetching banking data:', error)
      toast.error(t('banking.errorFetchingData'))
    } finally {
      setLoading(false)
    }
  }

  const handleAddBankAccount = async (accountData) => {
    try {
      await BankingService.addBankAccount(accountData)
      toast.success(t('banking.accountAdded'))
      setShowAddAccountModal(false)
      fetchBankingData()
    } catch (error) {
      console.error('Error adding bank account:', error)
      toast.error(t('banking.errorAddingAccount'))
    }
  }

  const handleAddPaymentMethod = async (paymentData) => {
    try {
      await BankingService.addPaymentMethod(paymentData)
      toast.success(t('banking.paymentMethodAdded'))
      setShowAddPaymentModal(false)
      fetchBankingData()
    } catch (error) {
      console.error('Error adding payment method:', error)
      toast.error(t('banking.errorAddingPaymentMethod'))
    }
  }

  const handleDeleteAccount = async (accountId) => {
    if (window.confirm(t('banking.confirmDeleteAccount'))) {
      try {
        await BankingService.deleteBankAccount(accountId)
        toast.success(t('banking.accountDeleted'))
        fetchBankingData()
      } catch (error) {
        console.error('Error deleting account:', error)
        toast.error(t('banking.errorDeletingAccount'))
      }
    }
  }

  const handleSetPrimaryAccount = async (accountId) => {
    try {
      await BankingService.setPrimaryAccount(accountId)
      toast.success(t('banking.primaryAccountSet'))
      fetchBankingData()
    } catch (error) {
      console.error('Error setting primary account:', error)
      toast.error(t('banking.errorSettingPrimary'))
    }
  }

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const getBankIcon = (bankName) => {
    // You can add specific bank icons here
    return <BanknotesIcon className="h-8 w-8 text-blue-600" />
  }

  const getAccountTypeBadge = (type) => {
    const typeClasses = {
      checking: 'bg-blue-100 text-blue-800',
      savings: 'bg-green-100 text-green-800',
      business: 'bg-purple-100 text-purple-800'
    }
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${typeClasses[type] || typeClasses.checking}`}>
        {t(`banking.accountType.${type}`)}
      </span>
    )
  }

  const AddBankAccountModal = () => {
    const [formData, setFormData] = useState({
      bank_name: '',
      account_number: '',
      routing_number: '',
      account_type: 'checking',
      account_holder_name: '',
      swift_code: '',
      iban: '',
      currency: 'USD'
    })

    const handleSubmit = (e) => {
      e.preventDefault()
      handleAddBankAccount(formData)
    }

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">{t('banking.addBankAccount')}</h2>
            <button
              onClick={() => setShowAddAccountModal(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('banking.bankName')}
                </label>
                <input
                  type="text"
                  value={formData.bank_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, bank_name: e.target.value }))}
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  placeholder={t('banking.bankNamePlaceholder')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('banking.accountType')}
                </label>
                <select
                  value={formData.account_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, account_type: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="checking">{t('banking.accountType.checking')}</option>
                  <option value="savings">{t('banking.accountType.savings')}</option>
                  <option value="business">{t('banking.accountType.business')}</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('banking.accountHolderName')}
              </label>
              <input
                type="text"
                value={formData.account_holder_name}
                onChange={(e) => setFormData(prev => ({ ...prev, account_holder_name: e.target.value }))}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                placeholder={t('banking.accountHolderNamePlaceholder')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('banking.accountNumber')}
                </label>
                <input
                  type="text"
                  value={formData.account_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, account_number: e.target.value }))}
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="**********"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('banking.routingNumber')}
                </label>
                <input
                  type="text"
                  value={formData.routing_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, routing_number: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="*********"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('banking.swiftCode')} ({t('common.optional')})
                </label>
                <input
                  type="text"
                  value={formData.swift_code}
                  onChange={(e) => setFormData(prev => ({ ...prev, swift_code: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="ABCDUS33"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('banking.iban')} ({t('common.optional')})
                </label>
                <input
                  type="text"
                  value={formData.iban}
                  onChange={(e) => setFormData(prev => ({ ...prev, iban: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="GB29 NWBK 6016 1331 9268 19"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('banking.currency')}
              </label>
              <select
                value={formData.currency}
                onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="EGP">EGP - Egyptian Pound</option>
                <option value="SAR">SAR - Saudi Riyal</option>
              </select>
            </div>

            <div className="flex space-x-4 pt-4">
              <button
                type="button"
                onClick={() => setShowAddAccountModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                {t('banking.addAccount')}
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <BanknotesIcon className="h-8 w-8 text-green-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {t('banking.bankAccount')}
                </h1>
                <p className="text-gray-600">
                  {t('banking.manageYourBankingDetails')}
                </p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowAddPaymentModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <CreditCardIcon className="h-5 w-5" />
                <span>{t('banking.addPaymentMethod')}</span>
              </button>
              <button
                onClick={() => setShowAddAccountModal(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-5 w-5" />
                <span>{t('banking.addBankAccount')}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Balance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BanknotesIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('banking.totalBalance')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(balance.total, balance.currency)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShieldCheckIcon className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('banking.availableBalance')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(balance.available, balance.currency)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('banking.pendingBalance')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(balance.pending, balance.currency)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bank Accounts */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('banking.bankAccounts')}</h2>
          
          {accounts.length === 0 ? (
            <div className="text-center py-8">
              <BanknotesIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('banking.noBankAccounts')}
              </h3>
              <p className="text-gray-500 mb-4">
                {t('banking.noBankAccountsDescription')}
              </p>
              <button
                onClick={() => setShowAddAccountModal(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                {t('banking.addFirstAccount')}
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {accounts.map((account) => (
                <div key={account.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      {getBankIcon(account.bank_name)}
                      <div>
                        <h3 className="font-medium text-gray-900">{account.bank_name}</h3>
                        <p className="text-sm text-gray-500">****{account.account_number.slice(-4)}</p>
                      </div>
                    </div>
                    {account.is_primary && (
                      <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                        {t('banking.primary')}
                      </span>
                    )}
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{t('banking.accountHolder')}:</span>
                      <span className="text-gray-900">{account.account_holder_name}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{t('banking.type')}:</span>
                      <span>{getAccountTypeBadge(account.account_type)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{t('banking.currency')}:</span>
                      <span className="text-gray-900">{account.currency}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedAccount(account)}
                      className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm hover:bg-gray-200 flex items-center justify-center space-x-1"
                    >
                      <EyeIcon className="h-4 w-4" />
                      <span>{t('banking.view')}</span>
                    </button>
                    
                    {!account.is_primary && (
                      <button
                        onClick={() => handleSetPrimaryAccount(account.id)}
                        className="flex-1 bg-green-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-green-700"
                      >
                        {t('banking.setPrimary')}
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDeleteAccount(account.id)}
                      className="bg-red-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Payment Methods */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('banking.paymentMethods')}</h2>
          
          {paymentMethods.length === 0 ? (
            <div className="text-center py-8">
              <CreditCardIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('banking.noPaymentMethods')}
              </h3>
              <p className="text-gray-500 mb-4">
                {t('banking.noPaymentMethodsDescription')}
              </p>
              <button
                onClick={() => setShowAddPaymentModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {t('banking.addFirstPaymentMethod')}
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {paymentMethods.map((method) => (
                <div key={method.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <CreditCardIcon className="h-8 w-8 text-blue-600" />
                      <div>
                        <h3 className="font-medium text-gray-900">{method.type}</h3>
                        <p className="text-sm text-gray-500">****{method.last_four}</p>
                      </div>
                    </div>
                    {method.is_default && (
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        {t('banking.default')}
                      </span>
                    )}
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{t('banking.expires')}:</span>
                      <span className="text-gray-900">{method.expires_at}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{t('banking.status')}:</span>
                      <span className={`text-sm font-medium ${method.is_verified ? 'text-green-600' : 'text-yellow-600'}`}>
                        {method.is_verified ? t('banking.verified') : t('banking.pending')}
                      </span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm hover:bg-gray-200">
                      {t('banking.edit')}
                    </button>
                    <button className="bg-red-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-red-700">
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Modals */}
        {showAddAccountModal && <AddBankAccountModal />}
      </div>
    </div>
  )
}

export default BankAccount
