<?php

namespace App\Services;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Models\SubscriptionPayment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SubscriptionService
{
    protected $stripeService;
    protected $paypalService;

    public function __construct()
    {
        // Initialize payment services
        // $this->stripeService = new StripeService();
        // $this->paypalService = new PayPalService();
    }

    /**
     * Create a new subscription for user
     */
    public function createSubscription(
        User $user,
        SubscriptionPlan $plan,
        string $paymentMethod,
        string $paymentToken = null,
        bool $autoRenew = true
    ): array {
        DB::beginTransaction();

        try {
            // Calculate subscription dates
            $startsAt = now();
            $endsAt = $startsAt->copy()->addDays($plan->duration_days);
            $trialEndsAt = $plan->trial_days > 0 ? $startsAt->copy()->addDays($plan->trial_days) : null;

            // Create subscription
            $subscription = UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'status' => $plan->trial_days > 0 ? 'trial' : 'pending',
                'starts_at' => $startsAt,
                'ends_at' => $endsAt,
                'trial_ends_at' => $trialEndsAt,
                'amount_paid' => $plan->price + $plan->setup_fee,
                'payment_method' => $paymentMethod,
                'auto_renew' => $autoRenew,
                'usage_limits' => $this->initializeUsageLimits($plan)
            ]);

            // Process payment if not in trial
            if ($plan->trial_days == 0 || $plan->price > 0) {
                $payment = $this->processPayment(
                    $subscription,
                    $plan->price + $plan->setup_fee,
                    $paymentMethod,
                    $paymentToken
                );

                if ($payment->isSuccessful()) {
                    $subscription->update(['status' => 'active']);
                } else {
                    $subscription->update(['status' => 'failed']);
                    throw new \Exception('Payment failed: ' . $payment->failure_reason);
                }
            }

            DB::commit();

            return [
                'subscription' => $subscription->fresh(['subscriptionPlan']),
                'payment' => $payment ?? null
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Subscription creation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(UserSubscription $subscription, string $reason = null): void
    {
        DB::beginTransaction();

        try {
            $subscription->cancel($reason);

            // Cancel recurring payments
            if ($subscription->stripe_subscription_id) {
                // Cancel Stripe subscription
                // $this->stripeService->cancelSubscription($subscription->stripe_subscription_id);
            }

            if ($subscription->paypal_subscription_id) {
                // Cancel PayPal subscription
                // $this->paypalService->cancelSubscription($subscription->paypal_subscription_id);
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Subscription cancellation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Renew a subscription
     */
    public function renewSubscription(
        UserSubscription $subscription,
        string $paymentMethod,
        string $paymentToken = null
    ): array {
        DB::beginTransaction();

        try {
            $plan = $subscription->subscriptionPlan;

            // Process payment
            $payment = $this->processPayment(
                $subscription,
                $plan->price,
                $paymentMethod,
                $paymentToken
            );

            if ($payment->isSuccessful()) {
                // Renew subscription
                $subscription->renew();
                
                // Reset usage limits
                $subscription->update([
                    'usage_limits' => $this->initializeUsageLimits($plan)
                ]);
            } else {
                throw new \Exception('Payment failed: ' . $payment->failure_reason);
            }

            DB::commit();

            return [
                'subscription' => $subscription->fresh(['subscriptionPlan']),
                'payment' => $payment
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Subscription renewal failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process payment for subscription
     */
    protected function processPayment(
        UserSubscription $subscription,
        float $amount,
        string $paymentMethod,
        string $paymentToken = null
    ): SubscriptionPayment {
        // Create payment record
        $payment = SubscriptionPayment::create([
            'user_subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'amount' => $amount,
            'currency' => 'USD',
            'status' => 'pending',
            'payment_method' => $paymentMethod,
            'transaction_id' => $this->generateTransactionId()
        ]);

        try {
            switch ($paymentMethod) {
                case 'stripe':
                    $result = $this->processStripePayment($payment, $paymentToken);
                    break;
                
                case 'paypal':
                    $result = $this->processPayPalPayment($payment, $paymentToken);
                    break;
                
                case 'bank_transfer':
                    $result = $this->processBankTransferPayment($payment);
                    break;
                
                default:
                    throw new \Exception('Unsupported payment method: ' . $paymentMethod);
            }

            if ($result['success']) {
                $payment->markAsCompleted($result['transaction_id']);
            } else {
                $payment->markAsFailed($result['error']);
            }

        } catch (\Exception $e) {
            $payment->markAsFailed($e->getMessage());
            Log::error('Payment processing failed: ' . $e->getMessage());
        }

        return $payment;
    }

    /**
     * Process Stripe payment
     */
    protected function processStripePayment(SubscriptionPayment $payment, string $paymentToken): array
    {
        try {
            // Mock Stripe payment processing
            // In real implementation, use Stripe SDK
            
            // Simulate payment processing
            $success = rand(1, 10) > 2; // 80% success rate for demo
            
            if ($success) {
                return [
                    'success' => true,
                    'transaction_id' => 'stripe_' . uniqid()
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Card declined'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process PayPal payment
     */
    protected function processPayPalPayment(SubscriptionPayment $payment, string $paymentToken): array
    {
        try {
            // Mock PayPal payment processing
            // In real implementation, use PayPal SDK
            
            // Simulate payment processing
            $success = rand(1, 10) > 1; // 90% success rate for demo
            
            if ($success) {
                return [
                    'success' => true,
                    'transaction_id' => 'paypal_' . uniqid()
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'PayPal payment failed'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process bank transfer payment
     */
    protected function processBankTransferPayment(SubscriptionPayment $payment): array
    {
        // Bank transfer requires manual verification
        return [
            'success' => true,
            'transaction_id' => 'bank_' . uniqid(),
            'requires_verification' => true
        ];
    }

    /**
     * Initialize usage limits for a plan
     */
    protected function initializeUsageLimits(SubscriptionPlan $plan): array
    {
        $limits = $plan->limits ?? [];
        $usage = [];

        foreach ($limits as $resource => $limit) {
            $usage[$resource] = 0;
        }

        return $usage;
    }

    /**
     * Generate unique transaction ID
     */
    protected function generateTransactionId(): string
    {
        return 'TXN_' . date('Ymd') . '_' . strtoupper(uniqid());
    }

    /**
     * Check if user can perform action based on subscription limits
     */
    public function canUserPerformAction(User $user, string $action): bool
    {
        $subscription = $user->activeSubscription();
        
        if (!$subscription) {
            return false;
        }

        return $subscription->canUse($action);
    }

    /**
     * Update user usage for specific action
     */
    public function updateUserUsage(User $user, string $action, int $amount = 1): void
    {
        $subscription = $user->activeSubscription();
        
        if ($subscription) {
            $subscription->updateUsage($action, $amount);
        }
    }

    /**
     * Get subscription analytics
     */
    public function getSubscriptionAnalytics(): array
    {
        return [
            'total_subscriptions' => UserSubscription::count(),
            'active_subscriptions' => UserSubscription::where('status', 'active')->count(),
            'trial_subscriptions' => UserSubscription::where('status', 'trial')->count(),
            'cancelled_subscriptions' => UserSubscription::where('status', 'cancelled')->count(),
            'monthly_revenue' => SubscriptionPayment::successful()
                ->whereMonth('created_at', now()->month)
                ->sum('amount'),
            'total_revenue' => SubscriptionPayment::successful()->sum('amount'),
            'popular_plans' => SubscriptionPlan::withCount('activeSubscriptions')
                ->orderBy('active_subscriptions_count', 'desc')
                ->take(5)
                ->get()
        ];
    }
}
