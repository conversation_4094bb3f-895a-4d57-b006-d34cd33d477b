<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the user profile associated with the user.
     */
    public function profile()
    {
        return $this->hasOne(UserProfile::class);
    }

    /**
     * Get the products that belong to the user.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the contracts where the user is the buyer.
     */
    public function buyerContracts()
    {
        return $this->hasMany(Contract::class, 'buyer_id');
    }

    /**
     * Get the contracts where the user is the seller.
     */
    public function sellerContracts()
    {
        return $this->hasMany(Contract::class, 'seller_id');
    }

    /**
     * Get the shipments where the user is the carrier.
     */
    public function shipments()
    {
        return $this->hasMany(Shipment::class, 'carrier_id');
    }

    /**
     * Get the user settings.
     */
    public function settings()
    {
        return $this->hasOne(UserSetting::class);
    }

    /**
     * Get the notification preferences.
     */
    public function notificationPreferences()
    {
        return $this->hasOne(NotificationPreference::class);
    }

    /**
     * Get the payment methods.
     */
    public function paymentMethods()
    {
        return $this->hasMany(PaymentMethod::class);
    }

    /**
     * Get the payments where the user is the payer.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'payer_id');
    }

    /**
     * Get the payments where the user is the payee.
     */
    public function receivedPayments()
    {
        return $this->hasMany(Payment::class, 'payee_id');
    }

    /**
     * Get the advertisements.
     */
    public function advertisements()
    {
        return $this->hasMany(Advertisement::class);
    }

    /**
     * Get the ratings given by the user.
     */
    public function givenRatings()
    {
        return $this->hasMany(Rating::class, 'rater_id');
    }

    /**
     * Get the ratings received by the user.
     */
    public function receivedRatings()
    {
        return $this->hasMany(Rating::class, 'rated_user_id');
    }

    /**
     * Get the reports made by the user.
     */
    public function reports()
    {
        return $this->hasMany(Report::class, 'reporter_id');
    }

    /**
     * Get the reports against the user.
     */
    public function reportedAgainst()
    {
        return $this->hasMany(Report::class, 'reported_user_id');
    }

    /**
     * Get the stores owned by the user.
     */
    public function stores()
    {
        return $this->hasMany(Store::class);
    }

    /**
     * Get the sent messages.
     */
    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * Get the received messages.
     */
    public function receivedMessages()
    {
        return $this->hasMany(Message::class, 'recipient_id');
    }

    /**
     * Get the forums created by the user.
     */
    public function forums()
    {
        return $this->hasMany(Forum::class, 'created_by');
    }

    /**
     * Get the forum posts created by the user.
     */
    public function forumPosts()
    {
        return $this->hasMany(ForumPost::class);
    }

    /**
     * Get the store ratings given by the user.
     */
    public function storeRatings()
    {
        return $this->hasMany(StoreRating::class);
    }

    /**
     * Get the security logs for the user.
     */
    public function securityLogs()
    {
        return $this->hasMany(SecurityLog::class);
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription()
    {
        return $this->hasOne(UserSubscription::class)
                    ->where('status', 'active')
                    ->where('starts_at', '<=', now())
                    ->where('ends_at', '>', now());
    }

    /**
     * Get the user's current subscription (active or trial).
     */
    public function currentSubscription()
    {
        return $this->hasOne(UserSubscription::class)
                    ->whereIn('status', ['active', 'trial'])
                    ->where('starts_at', '<=', now())
                    ->where(function($query) {
                        $query->where('ends_at', '>', now())
                              ->orWhere('trial_ends_at', '>', now());
                    });
    }

    /**
     * Get the user's subscription payments.
     */
    public function subscriptionPayments()
    {
        return $this->hasMany(SubscriptionPayment::class);
    }

    /**
     * Check if user has active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * Check if user is in trial period.
     */
    public function isInTrial(): bool
    {
        return $this->subscriptions()
                    ->where('status', 'trial')
                    ->where('trial_ends_at', '>', now())
                    ->exists();
    }

    /**
     * Check if user can perform specific action based on subscription.
     */
    public function canPerform(string $action): bool
    {
        $subscription = $this->currentSubscription;

        if (!$subscription) {
            return false;
        }

        return $subscription->canUse($action);
    }

    /**
     * Get user's subscription plan.
     */
    public function getSubscriptionPlan()
    {
        $subscription = $this->currentSubscription;
        return $subscription ? $subscription->subscriptionPlan : null;
    }

    /**
     * Determine if two-factor authentication is enabled for the user.
     *
     * @return bool
     */
    public function hasTwoFactorEnabled()
    {
        return ! is_null($this->two_factor_confirmed_at);
    }

    /**
     * Generate a new set of two-factor authentication recovery codes.
     *
     * @return array
     */
    public function generateRecoveryCodes()
    {
        $recoveryCodes = [];

        for ($i = 0; $i < 8; $i++) {
            $recoveryCodes[] = Str::random(10);
        }

        $this->two_factor_recovery_codes = json_encode($recoveryCodes);
        $this->save();

        return $recoveryCodes;
    }

    /**
     * Get the user's two-factor recovery codes.
     *
     * @return array
     */
    public function getRecoveryCodes()
    {
        return json_decode($this->two_factor_recovery_codes, true) ?? [];
    }

    /**
     * Verify a two-factor authentication recovery code.
     *
     * @param string $code
     * @return bool
     */
    public function verifyRecoveryCode($code)
    {
        $recoveryCodes = $this->getRecoveryCodes();

        $index = array_search($code, $recoveryCodes);

        if ($index !== false) {
            unset($recoveryCodes[$index]);
            $this->two_factor_recovery_codes = json_encode(array_values($recoveryCodes));
            $this->save();

            return true;
        }

        return false;
    }
}
