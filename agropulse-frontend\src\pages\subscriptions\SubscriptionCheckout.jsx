import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import {
  CreditCardIcon,
  LockClosedIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { SubscriptionService } from '../../services/SubscriptionService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

const SubscriptionCheckout = () => {
  const { planId } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  const [plan, setPlan] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('stripe');
  const [formData, setFormData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    email: '',
    billingAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: ''
    },
    agreeToTerms: false,
    autoRenew: true
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    fetchPlan();
  }, [planId]);

  const fetchPlan = async () => {
    try {
      const response = await SubscriptionService.getPlans();
      const selectedPlan = response.data.find(p => p.id === parseInt(planId));
      
      if (!selectedPlan) {
        toast.error(t('subscriptions.planNotFound'));
        navigate('/dashboard/subscriptions');
        return;
      }
      
      setPlan(selectedPlan);
    } catch (error) {
      console.error('Error fetching plan:', error);
      toast.error(t('subscriptions.fetchPlanError'));
      navigate('/dashboard/subscriptions');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (paymentMethod === 'stripe') {
      if (!formData.cardNumber) newErrors.cardNumber = t('validation.required');
      if (!formData.expiryDate) newErrors.expiryDate = t('validation.required');
      if (!formData.cvv) newErrors.cvv = t('validation.required');
      if (!formData.cardholderName) newErrors.cardholderName = t('validation.required');
    }

    if (!formData.email) newErrors.email = t('validation.required');
    if (!formData.agreeToTerms) newErrors.agreeToTerms = t('validation.agreeToTerms');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setProcessing(true);

    try {
      const paymentToken = paymentMethod === 'stripe' ? 'mock_stripe_token' : 'mock_paypal_token';
      
      const response = await SubscriptionService.subscribe(
        plan.id,
        paymentMethod,
        paymentToken,
        formData.autoRenew
      );

      toast.success(t('subscriptions.subscriptionSuccess'));
      navigate('/dashboard/subscriptions/success', {
        state: { subscription: response.data }
      });

    } catch (error) {
      console.error('Error processing subscription:', error);
      toast.error(t('subscriptions.subscriptionError'));
    } finally {
      setProcessing(false);
    }
  };

  const formatCardNumber = (value) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (!plan) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            {t('subscriptions.planNotFound')}
          </h3>
        </div>
      </div>
    );
  }

  const totalAmount = plan.price + plan.setup_fee;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <div className="px-6 py-8">
            <div className="flex items-center justify-center mb-8">
              <LockClosedIcon className="h-6 w-6 text-green-500 mr-2" />
              <h1 className="text-2xl font-bold text-gray-900">
                {t('subscriptions.secureCheckout')}
              </h1>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Order Summary */}
              <div className="order-2 lg:order-1">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  {t('subscriptions.orderSummary')}
                </h2>
                
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="font-medium text-gray-900">{plan.name}</h3>
                      <p className="text-sm text-gray-500">{plan.description}</p>
                      <p className="text-sm text-gray-500 mt-1">
                        {plan.billing_cycle_label} billing
                      </p>
                    </div>
                    <span className="text-lg font-medium text-gray-900">
                      ${plan.price}
                    </span>
                  </div>

                  {plan.setup_fee > 0 && (
                    <div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
                      <span className="text-sm text-gray-600">
                        {t('subscriptions.setupFee')}
                      </span>
                      <span className="text-sm text-gray-900">
                        ${plan.setup_fee}
                      </span>
                    </div>
                  )}

                  {plan.trial_days > 0 && (
                    <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex">
                        <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
                        <p className="text-sm text-green-700">
                          {t('subscriptions.freeTrialIncluded', { days: plan.trial_days })}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center text-lg font-medium text-gray-900">
                    <span>{t('subscriptions.total')}</span>
                    <span>${totalAmount}</span>
                  </div>
                </div>

                {/* Features */}
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">
                    {t('subscriptions.includedFeatures')}
                  </h3>
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Payment Form */}
              <div className="order-1 lg:order-2">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Payment Method Selection */}
                  <div>
                    <label className="text-base font-medium text-gray-900">
                      {t('subscriptions.paymentMethod')}
                    </label>
                    <div className="mt-4 space-y-4">
                      <div className="flex items-center">
                        <input
                          id="stripe"
                          name="paymentMethod"
                          type="radio"
                          value="stripe"
                          checked={paymentMethod === 'stripe'}
                          onChange={(e) => setPaymentMethod(e.target.value)}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                        />
                        <label htmlFor="stripe" className="ml-3 flex items-center">
                          <CreditCardIcon className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="text-sm font-medium text-gray-700">
                            {t('subscriptions.creditCard')}
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="paypal"
                          name="paymentMethod"
                          type="radio"
                          value="paypal"
                          checked={paymentMethod === 'paypal'}
                          onChange={(e) => setPaymentMethod(e.target.value)}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                        />
                        <label htmlFor="paypal" className="ml-3 flex items-center">
                          <span className="text-sm font-medium text-gray-700">
                            PayPal
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Credit Card Fields */}
                  {paymentMethod === 'stripe' && (
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="cardholderName" className="block text-sm font-medium text-gray-700">
                          {t('subscriptions.cardholderName')}
                        </label>
                        <input
                          type="text"
                          id="cardholderName"
                          name="cardholderName"
                          value={formData.cardholderName}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                            errors.cardholderName ? 'border-red-300' : ''
                          }`}
                        />
                        {errors.cardholderName && (
                          <p className="mt-1 text-sm text-red-600">{errors.cardholderName}</p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="cardNumber" className="block text-sm font-medium text-gray-700">
                          {t('subscriptions.cardNumber')}
                        </label>
                        <input
                          type="text"
                          id="cardNumber"
                          name="cardNumber"
                          value={formData.cardNumber}
                          onChange={(e) => {
                            const formatted = formatCardNumber(e.target.value);
                            setFormData(prev => ({ ...prev, cardNumber: formatted }));
                          }}
                          placeholder="1234 5678 9012 3456"
                          maxLength="19"
                          className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                            errors.cardNumber ? 'border-red-300' : ''
                          }`}
                        />
                        {errors.cardNumber && (
                          <p className="mt-1 text-sm text-red-600">{errors.cardNumber}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700">
                            {t('subscriptions.expiryDate')}
                          </label>
                          <input
                            type="text"
                            id="expiryDate"
                            name="expiryDate"
                            value={formData.expiryDate}
                            onChange={(e) => {
                              const formatted = formatExpiryDate(e.target.value);
                              setFormData(prev => ({ ...prev, expiryDate: formatted }));
                            }}
                            placeholder="MM/YY"
                            maxLength="5"
                            className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                              errors.expiryDate ? 'border-red-300' : ''
                            }`}
                          />
                          {errors.expiryDate && (
                            <p className="mt-1 text-sm text-red-600">{errors.expiryDate}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="cvv" className="block text-sm font-medium text-gray-700">
                            {t('subscriptions.cvv')}
                          </label>
                          <input
                            type="text"
                            id="cvv"
                            name="cvv"
                            value={formData.cvv}
                            onChange={handleInputChange}
                            placeholder="123"
                            maxLength="4"
                            className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                              errors.cvv ? 'border-red-300' : ''
                            }`}
                          />
                          {errors.cvv && (
                            <p className="mt-1 text-sm text-red-600">{errors.cvv}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Email */}
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      {t('subscriptions.email')}
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                        errors.email ? 'border-red-300' : ''
                      }`}
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                    )}
                  </div>

                  {/* Auto Renew */}
                  <div className="flex items-center">
                    <input
                      id="autoRenew"
                      name="autoRenew"
                      type="checkbox"
                      checked={formData.autoRenew}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <label htmlFor="autoRenew" className="ml-2 block text-sm text-gray-900">
                      {t('subscriptions.autoRenew')}
                    </label>
                  </div>

                  {/* Terms Agreement */}
                  <div className="flex items-center">
                    <input
                      id="agreeToTerms"
                      name="agreeToTerms"
                      type="checkbox"
                      checked={formData.agreeToTerms}
                      onChange={handleInputChange}
                      className={`h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded ${
                        errors.agreeToTerms ? 'border-red-300' : ''
                      }`}
                    />
                    <label htmlFor="agreeToTerms" className="ml-2 block text-sm text-gray-900">
                      {t('subscriptions.agreeToTerms')}
                    </label>
                  </div>
                  {errors.agreeToTerms && (
                    <p className="text-sm text-red-600">{errors.agreeToTerms}</p>
                  )}

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={processing}
                    className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${
                      processing ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {processing ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {t('subscriptions.processing')}
                      </div>
                    ) : (
                      <>
                        <LockClosedIcon className="h-4 w-4 mr-2" />
                        {t('subscriptions.completeSubscription')}
                      </>
                    )}
                  </button>

                  <p className="text-xs text-gray-500 text-center">
                    {t('subscriptions.securePayment')}
                  </p>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCheckout;
