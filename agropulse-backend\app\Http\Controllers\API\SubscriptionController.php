<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Models\SubscriptionPayment;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;

class SubscriptionController extends Controller
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Get all subscription plans
     */
    public function getPlans(Request $request): JsonResponse
    {
        try {
            $plans = SubscriptionPlan::active()
                ->orderBy('sort_order')
                ->orderBy('price')
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => $plans
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch subscription plans',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's current subscription
     */
    public function getCurrentSubscription(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $subscription = UserSubscription::with(['subscriptionPlan', 'payments'])
                ->where('user_id', $user->id)
                ->where(function($query) {
                    $query->where('status', 'active')
                          ->orWhere('status', 'trial');
                })
                ->first();

            if (!$subscription) {
                return response()->json([
                    'status' => 'success',
                    'data' => null,
                    'message' => 'No active subscription found'
                ]);
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'subscription' => $subscription,
                    'plan' => $subscription->subscriptionPlan,
                    'usage' => $subscription->usage_limits,
                    'days_remaining' => $subscription->days_remaining,
                    'is_active' => $subscription->isActive(),
                    'is_trial' => $subscription->isInTrial(),
                    'can_renew' => $subscription->isExpired() || $subscription->days_remaining <= 7
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch current subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Subscribe to a plan
     */
    public function subscribe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'plan_id' => 'required|exists:subscription_plans,id',
            'payment_method' => 'required|string|in:stripe,paypal,bank_transfer',
            'payment_token' => 'required_if:payment_method,stripe|string',
            'auto_renew' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = Auth::user();
            $plan = SubscriptionPlan::findOrFail($request->plan_id);

            // Check if user already has an active subscription
            $existingSubscription = UserSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->first();

            if ($existingSubscription) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User already has an active subscription'
                ], 422);
            }

            $result = $this->subscriptionService->createSubscription(
                $user,
                $plan,
                $request->payment_method,
                $request->payment_token,
                $request->auto_renew ?? true
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription created successfully',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = Auth::user();
            
            $subscription = UserSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->first();

            if (!$subscription) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No active subscription found'
                ], 404);
            }

            $this->subscriptionService->cancelSubscription($subscription, $request->reason);

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to cancel subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Renew subscription
     */
    public function renewSubscription(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|string|in:stripe,paypal,bank_transfer',
            'payment_token' => 'required_if:payment_method,stripe|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = Auth::user();
            
            $subscription = UserSubscription::with('subscriptionPlan')
                ->where('user_id', $user->id)
                ->whereIn('status', ['active', 'expired'])
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$subscription) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No subscription found to renew'
                ], 404);
            }

            $result = $this->subscriptionService->renewSubscription(
                $subscription,
                $request->payment_method,
                $request->payment_token
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Subscription renewed successfully',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to renew subscription',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subscription history
     */
    public function getSubscriptionHistory(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $subscriptions = UserSubscription::with(['subscriptionPlan', 'payments'])
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            return response()->json([
                'status' => 'success',
                'data' => $subscriptions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch subscription history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment history
     */
    public function getPaymentHistory(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $payments = SubscriptionPayment::with('userSubscription.subscriptionPlan')
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            return response()->json([
                'status' => 'success',
                'data' => $payments
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch payment history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check usage limits
     */
    public function checkUsage(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $subscription = UserSubscription::with('subscriptionPlan')
                ->where('user_id', $user->id)
                ->where('status', 'active')
                ->first();

            if (!$subscription) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No active subscription found'
                ], 404);
            }

            $limits = $subscription->subscriptionPlan->limits;
            $usage = $subscription->usage_limits ?? [];
            
            $usageData = [];
            foreach ($limits as $resource => $limit) {
                $currentUsage = $usage[$resource] ?? 0;
                $usageData[$resource] = [
                    'limit' => $limit,
                    'used' => $currentUsage,
                    'remaining' => $limit === -1 ? 'unlimited' : max(0, $limit - $currentUsage),
                    'percentage' => $limit === -1 ? 0 : min(100, ($currentUsage / $limit) * 100)
                ];
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'subscription' => $subscription,
                    'usage' => $usageData
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch usage data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
