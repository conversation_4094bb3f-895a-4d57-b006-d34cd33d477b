import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import {
  BanknotesIcon,
  CreditCardIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { BankingService } from '../../services/BankingService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';

const BankAccountManagement = () => {
  const { t } = useTranslation();
  const [accounts, setAccounts] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [activeTab, setActiveTab] = useState('accounts');
  const [showAccountNumbers, setShowAccountNumbers] = useState({});
  const [formData, setFormData] = useState({
    bank_name: '',
    account_holder_name: '',
    account_number: '',
    iban: '',
    swift_code: '',
    branch_name: '',
    account_type: 'checking',
    currency: 'USD',
    is_primary: false
  });
  const [errors, setErrors] = useState({});
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [accountsResponse, transactionsResponse] = await Promise.all([
        BankingService.getBankAccounts(),
        BankingService.getTransactions()
      ]);
      
      setAccounts(accountsResponse.data);
      setTransactions(transactionsResponse.data);
    } catch (error) {
      console.error('Error fetching banking data:', error);
      toast.error(t('banking.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.bank_name) newErrors.bank_name = t('validation.required');
    if (!formData.account_holder_name) newErrors.account_holder_name = t('validation.required');
    if (!formData.account_number) newErrors.account_number = t('validation.required');
    if (!formData.iban) newErrors.iban = t('validation.required');
    if (!formData.swift_code) newErrors.swift_code = t('validation.required');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setProcessing(true);

    try {
      if (selectedAccount) {
        await BankingService.updateBankAccount(selectedAccount.id, formData);
        toast.success(t('banking.updateSuccess'));
      } else {
        await BankingService.addBankAccount(formData);
        toast.success(t('banking.addSuccess'));
      }

      setShowAddModal(false);
      setSelectedAccount(null);
      setFormData({
        bank_name: '',
        account_holder_name: '',
        account_number: '',
        iban: '',
        swift_code: '',
        branch_name: '',
        account_type: 'checking',
        currency: 'USD',
        is_primary: false
      });
      fetchData();
    } catch (error) {
      console.error('Error saving bank account:', error);
      toast.error(t('banking.saveError'));
    } finally {
      setProcessing(false);
    }
  };

  const handleEdit = (account) => {
    setSelectedAccount(account);
    setFormData({
      bank_name: account.bank_name,
      account_holder_name: account.account_holder_name,
      account_number: account.account_number,
      iban: account.iban,
      swift_code: account.swift_code,
      branch_name: account.branch_name || '',
      account_type: account.account_type,
      currency: account.currency,
      is_primary: account.is_primary
    });
    setShowAddModal(true);
  };

  const handleDelete = async () => {
    if (!selectedAccount) return;

    setProcessing(true);
    try {
      await BankingService.deleteBankAccount(selectedAccount.id);
      toast.success(t('banking.deleteSuccess'));
      setShowDeleteModal(false);
      setSelectedAccount(null);
      fetchData();
    } catch (error) {
      console.error('Error deleting bank account:', error);
      toast.error(t('banking.deleteError'));
    } finally {
      setProcessing(false);
    }
  };

  const handleSetPrimary = async (accountId) => {
    try {
      await BankingService.setPrimaryAccount(accountId);
      toast.success(t('banking.setPrimarySuccess'));
      fetchData();
    } catch (error) {
      console.error('Error setting primary account:', error);
      toast.error(t('banking.setPrimaryError'));
    }
  };

  const toggleAccountNumberVisibility = (accountId) => {
    setShowAccountNumbers(prev => ({
      ...prev,
      [accountId]: !prev[accountId]
    }));
  };

  const maskAccountNumber = (accountNumber) => {
    if (!accountNumber) return '';
    const length = accountNumber.length;
    if (length <= 4) return accountNumber;
    return '*'.repeat(length - 4) + accountNumber.slice(-4);
  };

  const getAccountTypeIcon = (type) => {
    switch (type) {
      case 'checking':
        return BanknotesIcon;
      case 'savings':
        return BanknotesIcon;
      case 'business':
        return CreditCardIcon;
      default:
        return BanknotesIcon;
    }
  };

  const getTransactionStatusBadge = (status) => {
    const statusConfig = {
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircleIcon },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: ExclamationTriangleIcon },
      failed: { color: 'bg-red-100 text-red-800', icon: ExclamationTriangleIcon }
    };

    const config = statusConfig[status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {t(`banking.status.${status}`)}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <BanknotesIcon className="h-8 w-8 mr-3 text-green-600" />
              {t('banking.title')}
            </h1>
            <p className="mt-2 text-gray-600">
              {t('banking.description')}
            </p>
          </div>
          
          <button
            onClick={() => {
              setSelectedAccount(null);
              setFormData({
                bank_name: '',
                account_holder_name: '',
                account_number: '',
                iban: '',
                swift_code: '',
                branch_name: '',
                account_type: 'checking',
                currency: 'USD',
                is_primary: false
              });
              setShowAddModal(true);
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            {t('banking.addAccount')}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('accounts')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'accounts'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <BanknotesIcon className="h-4 w-4 inline mr-2" />
              {t('banking.accounts')}
            </button>
            
            <button
              onClick={() => setActiveTab('transactions')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'transactions'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <CreditCardIcon className="h-4 w-4 inline mr-2" />
              {t('banking.transactions')}
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'accounts' && (
            <div className="space-y-6">
              {accounts.length > 0 ? (
                accounts.map((account) => {
                  const AccountIcon = getAccountTypeIcon(account.account_type);
                  const isVisible = showAccountNumbers[account.id];
                  
                  return (
                    <div key={account.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className="bg-green-100 p-3 rounded-full">
                            <AccountIcon className="h-6 w-6 text-green-600" />
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="text-lg font-medium text-gray-900">
                                {account.bank_name}
                              </h3>
                              {account.is_primary && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  <ShieldCheckIcon className="h-3 w-3 mr-1" />
                                  {t('banking.primary')}
                                </span>
                              )}
                              {account.is_verified && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  {t('banking.verified')}
                                </span>
                              )}
                            </div>
                            
                            <p className="text-sm text-gray-600 mt-1">
                              {account.account_holder_name}
                            </p>
                            
                            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-500">
                                  {t('banking.accountNumber')}:
                                </span>
                                <div className="flex items-center space-x-2 mt-1">
                                  <span className="text-gray-900 font-mono">
                                    {isVisible ? account.account_number : maskAccountNumber(account.account_number)}
                                  </span>
                                  <button
                                    onClick={() => toggleAccountNumberVisibility(account.id)}
                                    className="text-gray-400 hover:text-gray-600"
                                  >
                                    {isVisible ? (
                                      <EyeSlashIcon className="h-4 w-4" />
                                    ) : (
                                      <EyeIcon className="h-4 w-4" />
                                    )}
                                  </button>
                                </div>
                              </div>
                              
                              <div>
                                <span className="font-medium text-gray-500">
                                  {t('banking.iban')}:
                                </span>
                                <p className="text-gray-900 font-mono mt-1">
                                  {account.iban}
                                </p>
                              </div>
                              
                              <div>
                                <span className="font-medium text-gray-500">
                                  {t('banking.swiftCode')}:
                                </span>
                                <p className="text-gray-900 font-mono mt-1">
                                  {account.swift_code}
                                </p>
                              </div>
                              
                              <div>
                                <span className="font-medium text-gray-500">
                                  {t('banking.accountType')}:
                                </span>
                                <p className="text-gray-900 mt-1">
                                  {t(`banking.accountTypes.${account.account_type}`)}
                                </p>
                              </div>
                              
                              {account.branch_name && (
                                <div>
                                  <span className="font-medium text-gray-500">
                                    {t('banking.branch')}:
                                  </span>
                                  <p className="text-gray-900 mt-1">
                                    {account.branch_name}
                                  </p>
                                </div>
                              )}
                              
                              <div>
                                <span className="font-medium text-gray-500">
                                  {t('banking.currency')}:
                                </span>
                                <p className="text-gray-900 mt-1">
                                  {account.currency}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          {!account.is_primary && (
                            <button
                              onClick={() => handleSetPrimary(account.id)}
                              className="text-sm text-blue-600 hover:text-blue-800"
                            >
                              {t('banking.setPrimary')}
                            </button>
                          )}
                          
                          <button
                            onClick={() => handleEdit(account)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <PencilIcon className="h-5 w-5" />
                          </button>
                          
                          <button
                            onClick={() => {
                              setSelectedAccount(account);
                              setShowDeleteModal(true);
                            }}
                            className="text-gray-400 hover:text-red-600"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-12">
                  <BanknotesIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {t('banking.noAccounts')}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {t('banking.noAccountsDescription')}
                  </p>
                  <div className="mt-6">
                    <button
                      onClick={() => setShowAddModal(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      {t('banking.addFirstAccount')}
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'transactions' && (
            <div className="space-y-4">
              {transactions.length > 0 ? (
                transactions.map((transaction) => (
                  <div key={transaction.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {transaction.description}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {new Date(transaction.created_at).toLocaleDateString()} • {transaction.bank_account?.bank_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {t('banking.reference')}: {transaction.reference_number}
                        </p>
                      </div>
                      <div className="text-right">
                        {getTransactionStatusBadge(transaction.status)}
                        <p className={`text-lg font-medium mt-1 ${
                          transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.type === 'credit' ? '+' : '-'}${transaction.amount}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {t('banking.noTransactions')}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {t('banking.noTransactionsDescription')}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Account Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {selectedAccount ? t('banking.editAccount') : t('banking.addAccount')}
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="bank_name" className="block text-sm font-medium text-gray-700">
                      {t('banking.bankName')} *
                    </label>
                    <input
                      type="text"
                      id="bank_name"
                      name="bank_name"
                      value={formData.bank_name}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                        errors.bank_name ? 'border-red-300' : ''
                      }`}
                    />
                    {errors.bank_name && (
                      <p className="mt-1 text-sm text-red-600">{errors.bank_name}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="account_holder_name" className="block text-sm font-medium text-gray-700">
                      {t('banking.accountHolderName')} *
                    </label>
                    <input
                      type="text"
                      id="account_holder_name"
                      name="account_holder_name"
                      value={formData.account_holder_name}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                        errors.account_holder_name ? 'border-red-300' : ''
                      }`}
                    />
                    {errors.account_holder_name && (
                      <p className="mt-1 text-sm text-red-600">{errors.account_holder_name}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="account_number" className="block text-sm font-medium text-gray-700">
                      {t('banking.accountNumber')} *
                    </label>
                    <input
                      type="text"
                      id="account_number"
                      name="account_number"
                      value={formData.account_number}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                        errors.account_number ? 'border-red-300' : ''
                      }`}
                    />
                    {errors.account_number && (
                      <p className="mt-1 text-sm text-red-600">{errors.account_number}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="iban" className="block text-sm font-medium text-gray-700">
                      {t('banking.iban')} *
                    </label>
                    <input
                      type="text"
                      id="iban"
                      name="iban"
                      value={formData.iban}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                        errors.iban ? 'border-red-300' : ''
                      }`}
                    />
                    {errors.iban && (
                      <p className="mt-1 text-sm text-red-600">{errors.iban}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="swift_code" className="block text-sm font-medium text-gray-700">
                      {t('banking.swiftCode')} *
                    </label>
                    <input
                      type="text"
                      id="swift_code"
                      name="swift_code"
                      value={formData.swift_code}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 ${
                        errors.swift_code ? 'border-red-300' : ''
                      }`}
                    />
                    {errors.swift_code && (
                      <p className="mt-1 text-sm text-red-600">{errors.swift_code}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="branch_name" className="block text-sm font-medium text-gray-700">
                      {t('banking.branch')}
                    </label>
                    <input
                      type="text"
                      id="branch_name"
                      name="branch_name"
                      value={formData.branch_name}
                      onChange={handleInputChange}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="account_type" className="block text-sm font-medium text-gray-700">
                      {t('banking.accountType')}
                    </label>
                    <select
                      id="account_type"
                      name="account_type"
                      value={formData.account_type}
                      onChange={handleInputChange}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="checking">{t('banking.accountTypes.checking')}</option>
                      <option value="savings">{t('banking.accountTypes.savings')}</option>
                      <option value="business">{t('banking.accountTypes.business')}</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
                      {t('banking.currency')}
                    </label>
                    <select
                      id="currency"
                      name="currency"
                      value={formData.currency}
                      onChange={handleInputChange}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="GBP">GBP</option>
                      <option value="EGP">EGP</option>
                      <option value="SAR">SAR</option>
                      <option value="AED">AED</option>
                    </select>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    id="is_primary"
                    name="is_primary"
                    type="checkbox"
                    checked={formData.is_primary}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_primary" className="ml-2 block text-sm text-gray-900">
                    {t('banking.setPrimaryAccount')}
                  </label>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    {t('common.cancel')}
                  </button>
                  
                  <button
                    type="submit"
                    disabled={processing}
                    className={`px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 ${
                      processing ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {processing ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {t('common.saving')}
                      </div>
                    ) : (
                      selectedAccount ? t('common.update') : t('common.add')
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        title={t('banking.deleteAccount')}
        message={t('banking.deleteConfirmation')}
        confirmText={t('common.delete')}
        cancelText={t('common.cancel')}
        type="danger"
        loading={processing}
      />
    </div>
  );
};

export default BankAccountManagement;
