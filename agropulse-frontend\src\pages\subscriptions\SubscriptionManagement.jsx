import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  CreditCardIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  XMarkIcon,
  ChartBarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { SubscriptionService } from '../../services/SubscriptionService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';

const SubscriptionManagement = () => {
  const { t } = useTranslation();
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [usage, setUsage] = useState(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState([]);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelling, setCancelling] = useState(false);
  const [cancelReason, setCancelReason] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      const [subscriptionResponse, usageResponse, historyResponse, paymentsResponse] = await Promise.all([
        SubscriptionService.getCurrentSubscription(),
        SubscriptionService.checkUsage(),
        SubscriptionService.getSubscriptionHistory(),
        SubscriptionService.getPaymentHistory()
      ]);

      setCurrentSubscription(subscriptionResponse.data);
      setUsage(usageResponse.data);
      setSubscriptionHistory(historyResponse.data.data);
      setPaymentHistory(paymentsResponse.data.data);
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      toast.error(t('subscriptions.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    setCancelling(true);
    try {
      await SubscriptionService.cancelSubscription(cancelReason);
      toast.success(t('subscriptions.cancelSuccess'));
      setShowCancelModal(false);
      setCancelReason('');
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error(t('subscriptions.cancelError'));
    } finally {
      setCancelling(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircleIcon },
      trial: { color: 'bg-blue-100 text-blue-800', icon: CheckCircleIcon },
      cancelled: { color: 'bg-red-100 text-red-800', icon: XMarkIcon },
      expired: { color: 'bg-gray-100 text-gray-800', icon: ExclamationTriangleIcon }
    };

    const config = statusConfig[status] || statusConfig.expired;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {t(`subscriptions.status.${status}`)}
      </span>
    );
  };

  const getUsagePercentage = (used, limit) => {
    if (limit === -1 || limit === null) return 0;
    return Math.min(100, (used / limit) * 100);
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('subscriptions.management')}
        </h1>
        <p className="mt-2 text-gray-600">
          {t('subscriptions.managementDescription')}
        </p>
      </div>

      {/* Current Subscription Overview */}
      {currentSubscription ? (
        <div className="bg-white shadow rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">
                {t('subscriptions.currentSubscription')}
              </h2>
              {getStatusBadge(currentSubscription.subscription?.status)}
            </div>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  {t('subscriptions.plan')}
                </h3>
                <p className="mt-1 text-lg font-semibold text-gray-900">
                  {currentSubscription.subscription?.subscriptionPlan?.name}
                </p>
                <p className="text-sm text-gray-500">
                  ${currentSubscription.subscription?.subscriptionPlan?.price}/{t('subscriptions.month')}
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  {t('subscriptions.nextBilling')}
                </h3>
                <p className="mt-1 text-lg font-semibold text-gray-900">
                  {new Date(currentSubscription.subscription?.ends_at).toLocaleDateString()}
                </p>
                <p className="text-sm text-gray-500">
                  {currentSubscription.days_remaining} {t('subscriptions.daysRemaining')}
                </p>
              </div>
              
              <div className="flex items-center space-x-3">
                <Link
                  to="/dashboard/subscriptions/plans"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <ArrowPathIcon className="h-4 w-4 mr-2" />
                  {t('subscriptions.changePlan')}
                </Link>
                
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                >
                  <XMarkIcon className="h-4 w-4 mr-2" />
                  {t('subscriptions.cancel')}
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg mb-8 p-6">
          <div className="text-center">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {t('subscriptions.noActiveSubscription')}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {t('subscriptions.noActiveSubscriptionDescription')}
            </p>
            <div className="mt-6">
              <Link
                to="/dashboard/subscriptions/plans"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                {t('subscriptions.choosePlan')}
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Usage Overview */}
      {usage && (
        <div className="bg-white shadow rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {t('subscriptions.usageOverview')}
            </h2>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Object.entries(usage.usage).map(([resource, data]) => {
                const percentage = getUsagePercentage(data.used, data.limit);
                
                return (
                  <div key={resource} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium text-gray-900">
                        {t(`subscriptions.${resource}`)}
                      </h3>
                      <span className="text-sm text-gray-500">
                        {data.used} / {data.limit === -1 ? t('subscriptions.unlimited') : data.limit}
                      </span>
                    </div>
                    
                    {data.limit !== -1 && (
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getUsageColor(percentage)}`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    )}
                    
                    {data.limit !== -1 && (
                      <p className="mt-1 text-xs text-gray-500">
                        {data.remaining} {t('subscriptions.remaining')}
                      </p>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('history')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <CalendarIcon className="h-4 w-4 inline mr-2" />
              {t('subscriptions.subscriptionHistory')}
            </button>
            
            <button
              onClick={() => setActiveTab('payments')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'payments'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <CreditCardIcon className="h-4 w-4 inline mr-2" />
              {t('subscriptions.paymentHistory')}
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'history' && (
            <div className="space-y-4">
              {subscriptionHistory.length > 0 ? (
                subscriptionHistory.map((subscription) => (
                  <div key={subscription.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {subscription.subscriptionPlan?.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {new Date(subscription.starts_at).toLocaleDateString()} - {new Date(subscription.ends_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        {getStatusBadge(subscription.status)}
                        <p className="text-sm text-gray-500 mt-1">
                          ${subscription.subscriptionPlan?.price}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-8">
                  {t('subscriptions.noSubscriptionHistory')}
                </p>
              )}
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="space-y-4">
              {paymentHistory.length > 0 ? (
                paymentHistory.map((payment) => (
                  <div key={payment.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {payment.userSubscription?.subscriptionPlan?.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {new Date(payment.created_at).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-gray-500">
                          {payment.payment_method_label}
                        </p>
                      </div>
                      <div className="text-right">
                        {getStatusBadge(payment.status)}
                        <p className="text-sm font-medium text-gray-900 mt-1">
                          ${payment.amount}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-8">
                  {t('subscriptions.noPaymentHistory')}
                </p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Cancel Subscription Modal */}
      <ConfirmationModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        onConfirm={handleCancelSubscription}
        title={t('subscriptions.cancelSubscription')}
        message={t('subscriptions.cancelConfirmation')}
        confirmText={t('subscriptions.confirmCancel')}
        cancelText={t('common.cancel')}
        type="danger"
        loading={cancelling}
      >
        <div className="mt-4">
          <label htmlFor="cancelReason" className="block text-sm font-medium text-gray-700">
            {t('subscriptions.cancelReason')} ({t('common.optional')})
          </label>
          <textarea
            id="cancelReason"
            name="cancelReason"
            rows={3}
            value={cancelReason}
            onChange={(e) => setCancelReason(e.target.value)}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
            placeholder={t('subscriptions.cancelReasonPlaceholder')}
          />
        </div>
      </ConfirmationModal>
    </div>
  );
};

export default SubscriptionManagement;
