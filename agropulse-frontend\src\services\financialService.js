import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

class FinancialService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })
  }

  // Get all transactions with filters
  async getTransactions(filters = {}) {
    try {
      const response = await this.api.get('/financial/transactions', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching transactions:', error)
      throw error
    }
  }

  // Get transaction summary
  async getTransactionSummary(filters = {}) {
    try {
      const response = await this.api.get('/financial/transactions/summary', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching transaction summary:', error)
      throw error
    }
  }

  // Get transaction details
  async getTransactionDetails(transactionId) {
    try {
      const response = await this.api.get(`/financial/transactions/${transactionId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching transaction details:', error)
      throw error
    }
  }

  // Export transactions
  async exportTransactions(filters = {}) {
    try {
      const response = await this.api.get('/financial/transactions/export', {
        params: filters,
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error exporting transactions:', error)
      throw error
    }
  }

  // Create transaction
  async createTransaction(transactionData) {
    try {
      const response = await this.api.post('/financial/transactions', transactionData)
      return response.data
    } catch (error) {
      console.error('Error creating transaction:', error)
      throw error
    }
  }

  // Update transaction
  async updateTransaction(transactionId, updateData) {
    try {
      const response = await this.api.put(`/financial/transactions/${transactionId}`, updateData)
      return response.data
    } catch (error) {
      console.error('Error updating transaction:', error)
      throw error
    }
  }

  // Get financial reports
  async getIncomeExpenseReport(filters = {}) {
    try {
      const response = await this.api.get('/financial-reports/income-expense', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching income expense report:', error)
      throw error
    }
  }

  async getSalesStatisticsReport(filters = {}) {
    try {
      const response = await this.api.get('/financial-reports/sales-statistics', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching sales statistics report:', error)
      throw error
    }
  }

  async getSupplierCustomerReport(filters = {}) {
    try {
      const response = await this.api.get('/financial-reports/supplier-customer', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching supplier customer report:', error)
      throw error
    }
  }

  // Get payment analytics
  async getPaymentAnalytics(period = '30d') {
    try {
      const response = await this.api.get('/financial/analytics', { params: { period } })
      return response.data
    } catch (error) {
      console.error('Error fetching payment analytics:', error)
      throw error
    }
  }

  // Get cash flow data
  async getCashFlow(period = '12m') {
    try {
      const response = await this.api.get('/financial/cash-flow', { params: { period } })
      return response.data
    } catch (error) {
      console.error('Error fetching cash flow:', error)
      throw error
    }
  }

  // Get profit and loss statement
  async getProfitLoss(startDate, endDate) {
    try {
      const response = await this.api.get('/financial/profit-loss', {
        params: { start_date: startDate, end_date: endDate }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching profit loss:', error)
      throw error
    }
  }

  // Get balance sheet
  async getBalanceSheet(date) {
    try {
      const response = await this.api.get('/financial/balance-sheet', {
        params: { date }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching balance sheet:', error)
      throw error
    }
  }

  // Get tax information
  async getTaxInformation(year) {
    try {
      const response = await this.api.get('/financial/tax-info', {
        params: { year }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching tax information:', error)
      throw error
    }
  }

  // Generate invoice
  async generateInvoice(invoiceData) {
    try {
      const response = await this.api.post('/financial/invoices', invoiceData)
      return response.data
    } catch (error) {
      console.error('Error generating invoice:', error)
      throw error
    }
  }

  // Get invoices
  async getInvoices(filters = {}) {
    try {
      const response = await this.api.get('/financial/invoices', { params: filters })
      return response.data
    } catch (error) {
      console.error('Error fetching invoices:', error)
      throw error
    }
  }

  // Download invoice
  async downloadInvoice(invoiceId) {
    try {
      const response = await this.api.get(`/financial/invoices/${invoiceId}/download`, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error downloading invoice:', error)
      throw error
    }
  }

  // Get budget information
  async getBudget(year) {
    try {
      const response = await this.api.get('/financial/budget', {
        params: { year }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching budget:', error)
      throw error
    }
  }

  // Create/Update budget
  async saveBudget(budgetData) {
    try {
      const response = await this.api.post('/financial/budget', budgetData)
      return response.data
    } catch (error) {
      console.error('Error saving budget:', error)
      throw error
    }
  }

  // Get financial goals
  async getFinancialGoals() {
    try {
      const response = await this.api.get('/financial/goals')
      return response.data
    } catch (error) {
      console.error('Error fetching financial goals:', error)
      throw error
    }
  }

  // Create financial goal
  async createFinancialGoal(goalData) {
    try {
      const response = await this.api.post('/financial/goals', goalData)
      return response.data
    } catch (error) {
      console.error('Error creating financial goal:', error)
      throw error
    }
  }

  // Update financial goal
  async updateFinancialGoal(goalId, updateData) {
    try {
      const response = await this.api.put(`/financial/goals/${goalId}`, updateData)
      return response.data
    } catch (error) {
      console.error('Error updating financial goal:', error)
      throw error
    }
  }

  // Delete financial goal
  async deleteFinancialGoal(goalId) {
    try {
      const response = await this.api.delete(`/financial/goals/${goalId}`)
      return response.data
    } catch (error) {
      console.error('Error deleting financial goal:', error)
      throw error
    }
  }

  // Get expense categories
  async getExpenseCategories() {
    try {
      const response = await this.api.get('/financial/expense-categories')
      return response.data
    } catch (error) {
      console.error('Error fetching expense categories:', error)
      throw error
    }
  }

  // Get income sources
  async getIncomeSources() {
    try {
      const response = await this.api.get('/financial/income-sources')
      return response.data
    } catch (error) {
      console.error('Error fetching income sources:', error)
      throw error
    }
  }

  // Get financial dashboard data
  async getDashboardData() {
    try {
      const response = await this.api.get('/financial/dashboard')
      return response.data
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      throw error
    }
  }

  // Get recurring transactions
  async getRecurringTransactions() {
    try {
      const response = await this.api.get('/financial/recurring-transactions')
      return response.data
    } catch (error) {
      console.error('Error fetching recurring transactions:', error)
      throw error
    }
  }

  // Create recurring transaction
  async createRecurringTransaction(transactionData) {
    try {
      const response = await this.api.post('/financial/recurring-transactions', transactionData)
      return response.data
    } catch (error) {
      console.error('Error creating recurring transaction:', error)
      throw error
    }
  }

  // Get financial alerts
  async getFinancialAlerts() {
    try {
      const response = await this.api.get('/financial/alerts')
      return response.data
    } catch (error) {
      console.error('Error fetching financial alerts:', error)
      throw error
    }
  }

  // Create financial alert
  async createFinancialAlert(alertData) {
    try {
      const response = await this.api.post('/financial/alerts', alertData)
      return response.data
    } catch (error) {
      console.error('Error creating financial alert:', error)
      throw error
    }
  }
}

export const financialService = new FinancialService()
export { FinancialService }
